import winreg

def set_registry_dword(key_path, value_name, new_value):
    """修改或创建指定路径的REG_DWORD注册表值"""
    try:
        # 打开指定的注册表项
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            key_path,
            0,
            winreg.KEY_SET_VALUE
        )
        
        # 设置DWORD值
        winreg.SetValueEx(key, value_name, 0, winreg.REG_DWORD, new_value)
        print(f"成功修改: {key_path}\\{value_name} = {new_value}")
        
        # 关闭注册表项
        winreg.CloseKey(key)
        return True
        
    except Exception as e:
        print(f"修改失败 {key_path}\\{value_name}: {str(e)}")
        return False

if __name__ == "__main__":
    # 需要修改的注册表项列表
    registry_changes = [
        # 1. 由询问修改为默认
        {
            "path": r"SOFTWARE\SAP\SAPGUI Front\SAP Frontend Server\Security",
            "name": "DefaultAction",
            "value": 0
        },
        # 2. 脚本连接警告设置
        {
            "path": r"SOFTWARE\SAP\SAPGUI Front\SAP Frontend Server\Scripting",
            "name": "ShowNativeWinDlgs",
            "value": 0
        },
        # 3. 关闭Attach警告
        {
            "path": r"SOFTWARE\SAP\SAPGUI Front\SAP Frontend Server\Security",
            "name": "WarnOnAttach",
            "value": 0
        },
        # 4. 关闭连接警告
        {
            "path": r"SOFTWARE\SAP\SAPGUI Front\SAP Frontend Server\Security",
            "name": "WarnOnConnection",
            "value": 0
        }
    ]
    
    # 执行所有修改
    print("开始修改SAP GUI注册表设置...")
    for change in registry_changes:
        set_registry_dword(change["path"], change["name"], change["value"])
    
    print("修改操作完成。")
    