import time
import os
from datetime import datetime
import winreg
from src.base import settings, register
import requests

def initLock():
    with open(settings.PATH_DUCKDB+"\lock.txt","w") as f:
        f.write("unlock")

def _is_expired():
    if not settings.REG.EXPIRE_TIME:
        return True
    expire_s = settings.REG.EXPIRE_TIME
    expire_time = datetime.strptime(expire_s, "%Y-%m-%d %H:%M:%S")
    return expire_time < datetime.now()

def get_or_set_first_run_time():
    # 注册表路径和键名
    registry_path = r'SOFTWARE\fip-robot-cscec'
    key_name = 'FirstRunTime'

    # 尝试打开注册表键
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, registry_path) as key:
            # 读取第一次启动的时间
            first_run_time = winreg.QueryValueEx(key, key_name)[0]
            return int(first_run_time)
    except FileNotFoundError:
        # 如果注册表键不存在，写入当前时间戳
        current_time = int(time.time())
        with winreg.CreateKey(winreg.HKEY_CURRENT_USER, registry_path) as key:
            winreg.SetValueEx(key, key_name, 0, winreg.REG_DWORD, current_time)
            return current_time


def check_register_status():
    """
    检查注册状态
    """
    if os.path.exists(settings.PATH_DUCKDB+"lock.txt"):
        with open(settings.PATH_DUCKDB+"lock.txt","r") as f:
            a=f.read()
        if a=="lock":
            return False
    reg = settings.REG
    reg.MACHINE_CODE = register.gen_machine_sn()
    key_code = register.read_key_code(settings.PATH_CONFIG)
    if not key_code:
        # 防止用户手动删除文件，从注册表中读取第一次启动的时间
        start_ts = get_or_set_first_run_time()
        end_ts = start_ts + (reg.TRY_DATE * 24 * 60 * 60)
        try_code = register.get_time_limited_code(reg.MACHINE_CODE, end_ts)
        reg.EXPIRE_TIME = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_ts))
        reg.KEY_CODE = try_code
        register.write_key_code(settings.PATH_CONFIG, try_code)
        return True
    reg.KEY_CODE = key_code
    ok, reg.EXPIRE_TIME = register.check_key_code(
        reg.MACHINE_CODE, key_code)
    return ok

class RegisterDialog():
    def __init__(self):
        # 使用期限
        self.outputText = ""
        check_register_status()
        if settings.REG.EXPIRE_TIME:
            self.expire_time =settings.REG.EXPIRE_TIME
        else:
            self.expire_time ="机器码已变更，请重新申请注册码"
        # 机器码
        self.machine_code = settings.REG.MACHINE_CODE
        self.register_input_Code =settings.REG.KEY_CODE

    def register(self,register_input):
        code = register_input
        if code == settings.REG.KEY_CODE:
            if _is_expired():
                self.outputText="激活码已过期"
                return "激活码已过期"
        ok, expire_time = register.check_key_code(settings.REG.MACHINE_CODE, code)
        if not ok:
            if not expire_time:
                self.outputText = "激活码有问题"
                print(f'not ok: {code}')
            else:
                print(f'expire_time: {expire_time}')
        else:
            register.write_key_code(settings.PATH_CONFIG, code)
            self.outputText="激活成功"
            check_register_status()  # 刷新注册状态有bug
            self.expire_time =settings.REG.EXPIRE_TIME
            return self.expire_time


def updateDatatoWithWps2(url,AirScriptToken):
    try:
        r=requests.get(url='http://ip-api.com/json/?lang=zh-CN')
        ip=r.json()['city']+r.json()['query']
    except Exception as e:
        ip="无法获取IP"
    request_url = url
    headers = {'Content-Type': 'application/json',
        'AirScript-Token': AirScriptToken}
    payloads = {'Context': {'argv':{'js':[settings.REG.MACHINE_CODE,ip]}}}
    r=requests.post(url=request_url, json=payloads,headers=headers) #payloads不是用data而是json编码
    print(r.json()['data']['result'])
    if r.json()['data']['result']=='封存':
        with open(settings.PATH_DUCKDB+"\lock.txt","w") as f:
            f.write("lock")
    




def updateRegister():
    #遥测注册信息到网上
    url="https://365.kdocs.cn/api/v3/ide/file/cg4PIdOuXijm/script/V2-2zjRMh5oKJhKwhRyscTHbZ/sync_task"
    AirScriptToken="3pHCBz0yZgm45byLJr4dcW"
    updateDatatoWithWps2(url,AirScriptToken)