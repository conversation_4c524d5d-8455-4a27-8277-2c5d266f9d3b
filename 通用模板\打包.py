import datetime
from pathlib import Path
import os
import shutil
import compileall
import sys
sys.path.append(".")
 
def package(root_path="./test_project/",reserve_one=False,packName="pack"):
    """
    编译根目录下的包括子目录里的所有py文件成pyc文件到新的文件夹下
    如果只保留一份文件，请将需编译的目录备份，因为本程序会清空该源文件夹
    :param root_path: 需编译的目录
    :param reserve_one: 是否只保留一个目录
    :return:
    """
    root = Path(root_path)
 
    # 先删除根目录下的pyc文件和__pycache__文件夹
    for src_file in root.rglob("*.pyc"):
        os.remove(src_file)
    for src_file in root.rglob("__pycache__"):
        os.rmdir(src_file)
 
    current_day = datetime.date.today()  # 当前日期
    edition = "1.0"  # 设置版本号
 
    dest = Path(root.parent / f"{packName}")  # 目标文件夹名称
 
    if os.path.exists(dest):
        shutil.rmtree(dest)
 
    shutil.copytree(root, dest)
 
    compileall.compile_dir(root, force=True)  # 将项目下的py都编译成pyc文件
 
    for src_file in root.glob("**/*.pyc"):  # 遍历所有pyc文件
        relative_path = src_file.relative_to(root)  # pyc文件对应模块文件夹名称
        dest_folder = dest / str(relative_path.parent.parent)  # 在目标文件夹下创建同名模块文件夹
        os.makedirs(dest_folder, exist_ok=True)
        dest_file = dest_folder / (src_file.stem.rsplit(".", 1)[0] + src_file.suffix)  # 创建同名文件
        print(f"install {relative_path}")
        shutil.copyfile(src_file, dest_file)  # 将pyc文件复制到同名文件
 
    # 清除源py文件
    for src_file in dest.rglob("*.py"):
        os.remove(src_file)
 
    # 清除源目录文件
    if reserve_one:
        if os.path.exists(root):
            shutil.rmtree(root)
        dest.rename(root)
 
def compressIndexFilesToZip(root_path, save_path):
    """
    便利该文件夹及子文件夹，将index开头的文件合并打包成zip
    :param root_path: 需要便利的文件夹
    :param save_path: 保存的文件夹
    :return:
    """
    root = Path(root_path)
    zip_path = save_path + "/web.zip"
    import zipfile
    with zipfile.ZipFile(zip_path, 'w') as z:
        for root, dirs, files in os.walk(root):
            for file in files:
                if file.startswith("index"):
                    z.write(os.path.join(root, file), os.path.relpath(os.path.join(root, file), root_path))

def compressModuleToZip(root_path, save_path):
    """
    便利该文件夹及子文件夹，将index开头的文件合并打包成zip
    :param root_path: 需要便利的文件夹
    :param save_path: 保存的文件夹
    :return:
    """
    root = Path(root_path)
    zip_path = save_path + "/web_js.zip"
    import zipfile
    with zipfile.ZipFile(zip_path, 'w') as z:
        for root, dirs, files in os.walk(root):
            for file in files:
                if not file.startswith("index"):
                    z.write(os.path.join(root, file), os.path.relpath(os.path.join(root, file), root_path))

def compressJStoZip(root_path, save_path):
    root = Path(root_path)
    zip_path = save_path + "/web.zip"
    import zipfile
    with zipfile.ZipFile(zip_path, 'w') as z:
        for current_root, dirs, files in os.walk(root_path):
            # Only process files in js and assets directories or index files in root
            if 'js' in current_root.split(os.sep) or 'assets' in current_root.split(os.sep) or current_root == str(root_path):
                for file in files:
                    if file.endswith(".map"):
                        continue
                    if current_root == str(root_path) and not file.startswith('index'):
                        continue  # Skip non-index files in root directory
                    file_path = os.path.join(current_root, file)
                    arcname = os.path.relpath(file_path, root_path)
                    z.write(file_path, arcname)

def compressDB():
    import sqlite3
    web_conn = sqlite3.connect(r"D:\infoTech\fip-rpa-robot-oldscriptref\data\InternalData\Duckdb\web.db")
    maxRowId = web_conn.execute("SELECT MAX(rowid) FROM 表格缓存").fetchone()[0]
    text = web_conn.execute("SELECT data FROM 表格缓存 WHERE rowid = ?", (maxRowId,)).fetchone()
    with open(r"./data/打包工具/web.txt", "w", encoding="utf-8") as f:
        f.write(text[0])

def compressVision():
    import src.base.settings as settings
    version=settings.version
    status=settings.status
    des="增强稳定性，修复bug,修复资金计划上传"
    with open(r"./data/打包工具/version.txt", "w", encoding="utf-8") as f:
        f.write(f"version={version}\nstatus={status}\ndescription={des}")
    
if __name__ == '__main__':
    package(root_path=r".\src",reserve_one=False,packName=r"data\ftools\src")
    #将文件夹打包成zip压缩包
    shutil.make_archive("./data/打包工具/ftools", 'zip', "./data/ftools")
    #删除fools文件夹
    shutil.rmtree(r"./data/ftools")
    #compressIndexFilesToZip(r"D:\infoTech\fipweb\dist", r"./data/打包工具")
    #compressModuleToZip(r"D:\infoTech\fipweb\dist\js", r"./data/打包工具")
    compressJStoZip(r"D:\infoTech\fipweb\dist", r"./data/打包工具")
    compressDB()
    compressVision()
    print("打包完成")
    
