
from playwright.sync_api import Playwright, sync_playwright
import src.utils.sapPublic.GetSAPSession as GetSAPSession
from src.utils.DB.midIntrSQLiteDB import excelDB
def autofill():
    db=excelDB()
    df=db.getDataframe("独立结账模板收入成本")
    session = GetSAPSession.creatSAP()
    session.StartTransaction("ZARAP0003")
    for i,row in df.iterrows():
        d=row.to_dict()
        if d["SAP收入测算是否"]=="是":
            print("开始序号"+str(i+1)+"行")
            db.updateData("独立结账模板收入成本","SAP收入测算是否","开始执行",i+1)
            session.findById("wnd[0]/usr/ctxtS_PRCTR-LOW").text = d["利润中心编码"]#填写利润中心
            projectCode=d["项目编码"] if isinstance(d["项目编码"],str) else str(int(d["项目编码"]))
            session.findById("wnd[0]/usr/ctxtS_PSPNR-LOW").text = projectCode #填写项目编码
            session.findById("wnd[0]/usr/txtS_GJAHR-LOW").text = d["年份"] #年份
            session.findById("wnd[0]/usr/txtS_MONAT-LOW").text = d["月份"] #月份
            session.findById("wnd[0]/usr/ctxtS_PSPNR-LOW").setFocus()
            session.findById("wnd[0]/usr/radP_CSGN").select() #选中收入测算功能
            session.findById("wnd[0]/tbar[1]/btn[8]").press() #执行
            predictIncome=str(round(d["结账预计收入"],4)) if isinstance(d["结账预计收入"],(int,float)) else d["结账预计收入"].replace(",","")
            predictCost=str(round(d["结账预计成本"],4)) if isinstance(d["结账预计成本"],(int,float)) else d["结账预计成本"].replace(",","")
            session.findById("wnd[0]/usr/subSUB02:ZARAPRP0002:9220/txtGS_CSD-VERDATA-CB_YJZ").text = predictCost#填写预计成本
            session.findById("wnd[0]/usr/subSUB02:ZARAPRP0002:9220/txtGS_CSD-VERDATA-SR_YJZ").text = predictIncome#填写预计收入
            session.findById("wnd[0]/usr/subSUB02:ZARAPRP0002:9220/txtGS_CSD-VERDATA-SR_YJZ").setFocus()
            session.findById("wnd[0]/tbar[1]/btn[13]").press() #收入测算保存
            sapInfo=session.findById("wnd[0]/sbar").text 
            cumulativeCost=session.findById("wnd[0]/usr/subSUB02:ZARAPRP0002:9220/txtGS_CSD-VERDATA-CB_LJCS").text
            totalIncome=session.findById("wnd[0]/usr/subSUB02:ZARAPRP0002:9220/txtGS_CSD-VERDATA-SR_LJCS").text
            if isinstance(d["按比例结账成本"],(int,float)) and isinstance(d["按比例结账收入"],(int,float)):
                if abs(d["按比例结账成本"]-float(cumulativeCost.replace(",","")))<0.01 and abs(d["按比例结账收入"]-float(totalIncome.replace(",","")) )<0.01:
                    print("一致")
                else:
                    print("警告，结账预计成本和结账预计收入与SAP累计成本和累计收入不一致")
            session.findById("wnd[0]/tbar[1]/btn[14]").press() #收入测算提交
            sapInfo=session.findById("wnd[0]/sbar").text  
            db.updateData("独立结账模板收入成本","SAP收入测算是否","执行完毕",i+1)     
    session.findById("wnd[0]").Close()