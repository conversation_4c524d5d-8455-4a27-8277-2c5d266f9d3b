<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>信小财 RPA 财务机器人 - 启动中...</title>
<style>
  :root {
    /* 与welcome_screen.py保持一致的配色方案 */
    --primary-blue: #2196F3;      /* BLUE_400 */
    --light-blue: #90CAF9;        /* BLUE_300 */
    --dark-blue: #1976D2;         /* BLUE_500 */
    --indigo-light: #C5CAE9;      /* INDIGO_200 */
    --blue-grey-800: #37474F;     /* BLUE_GREY_800 */
    --blue-grey-600: #546E7A;     /* BLUE_GREY_600 */
    --blue-grey-50: #ECEFF1;      /* BLUE_50 */
    --indigo-50: #E8EAF6;         /* INDIGO_50 */
    --purple-50: #F3E5F5;         /* PURPLE_50 */
    --shadow-blue: #BB<PERSON>FB;       /* BLUE_200 */
  }

  body, html {
    height: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--blue-grey-50), var(--indigo-50), var(--purple-50));
    overflow: hidden;
    font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
  }

  .background-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
  }

  .scene {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1; /* 确保前景内容在背景之上 */
  }

  /* 粒子效果 - 与welcome_screen.py一致 */
  .particle {
    position: absolute;
    background: var(--light-blue);
    border-radius: 50%;
    opacity: 0.6;
    animation: float-particle 20s infinite linear;
  }

  /* 电路线条效果 - 模拟welcome_screen.py的circuit_lines */
  .circuit-line {
    position: absolute;
    background: var(--indigo-light);
    opacity: 0.4;
    animation: circuit-glow 3s ease-in-out infinite;
    border-radius: 1px; /* 添加轻微圆角 */
  }

  /* 水平线条效果 - 确保屏幕宽度覆盖 */
  .horizontal-line {
    position: absolute;
    width: 100vw;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--indigo-light), transparent);
    opacity: 0.3;
    animation: horizontal-sweep 4s ease-in-out infinite;
  }

  /* 垂直线条效果 */
  .vertical-line {
    position: absolute;
    width: 1px;
    height: 100vh;
    background: linear-gradient(180deg, transparent, var(--indigo-light), transparent);
    opacity: 0.3;
    animation: vertical-sweep 5s ease-in-out infinite;
  }

  /* 扫描线效果 - 模拟welcome_screen.py的scan_line */
  .scan-line {
    position: absolute;
    width: 100vw; /* 使用视口宽度确保占满屏幕 */
    height: 2px;
    background: var(--primary-blue);
    opacity: 0.5;
    top: 0;
    left: 0; /* 确保从屏幕最左边开始 */
    animation: scan-move 3s linear infinite;
  }

  .loader-container {
    text-align: center;
    color: var(--blue-grey-800);
    z-index: 10;
    position: relative;
  }

  /* Logo容器 - 与welcome_screen.py保持一致的设计 */
  .logo-wrapper {
    position: relative;
    width: 300px; /* 增加尺寸 */
    height: 300px; /* 增加尺寸 */
    margin: 0 auto 40px;
    opacity: 0;
    transform: scale(0.5);
    animation: logo-entrance 1s ease-out forwards 0.5s;
  }

  .logo-outer {
    position: absolute;
    width: 100%; /* 使用百分比以适应容器 */
    height: 100%; /* 使用百分比以适应容器 */
    border-radius: 50%;
    border: 1px solid var(--shadow-blue);
    opacity: 0.4;
    animation: pulse-ring 4s ease-in-out infinite;
  }

  .logo-middle {
    position: absolute;
    width: 280px; /* 增加尺寸 */
    height: 280px; /* 增加尺寸 */
    top: 10px;
    left: 10px;
    border-radius: 50%;
    border: 2px solid var(--primary-blue);
    background: rgba(33, 150, 243, 0.05);
  }

  .logo-inner {
    position: absolute;
    width: 260px; /* 增加尺寸 */
    height: 260px; /* 增加尺寸 */
    top: 20px;
    left: 20px;
    border-radius: 50%;
    background: var(--blue-grey-50); /* 改成更柔和的浅蓝灰色 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--blue-grey-800); /* 改成深色文字 */
    font-weight: bold;
    box-shadow: 0 0 15px var(--shadow-blue) inset; /* 调整阴影颜色 */
    overflow: hidden;
  }

  .logo-icon {
    font-size: 3.5em;
    margin-bottom: 5px;
    animation: icon-pulse 2s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(255,255,255,0.3));
  }

  .logo-text {
    font-size: 0.8em;
    letter-spacing: 1px;
    text-align: center;
    line-height: 1.2;
    text-shadow: 0 0 5px rgba(255,255,255,0.5);
  }

  /* 如果有rpa.ico文件，使用图片替代emoji */
  .logo-image {
    width: 120px;
    height: 120px;
    margin-bottom: 10px;
    animation: icon-pulse 2s ease-in-out infinite;
    filter: drop-shadow(0 0 15px rgba(255,255,255,0.3));
  }

  /* 标题样式 - 与welcome_screen.py一致 */
  .title {
    font-size: 48px;
    font-weight: bold;
    color: var(--blue-grey-800);
    margin: 20px 0;
    opacity: 0;
    animation: fade-in 1s ease-out forwards 1.3s;
    text-shadow: 1px 8px var(--shadow-blue);
  }

  .subtitle {
    font-size: 20px;
    color: var(--blue-grey-600);
    margin: 20px 0 80px;
    opacity: 0;
    animation: fade-in 1s ease-out forwards 1.8s;
  }

  /* 进度条样式 - 与welcome_screen.py一致 */
  .progress-container {
    width: 400px;
    height: 6px;
    background: rgba(187, 222, 251, 0.3);
    border-radius: 3px;
    margin: 0 auto 20px;
    overflow: hidden;
    opacity: 0;
    animation: fade-in 1s ease-out forwards 2.3s;
  }

  .progress-bar {
    width: 0%;
    height: 100%;
    background: var(--dark-blue);
    border-radius: 3px;
    animation: progress-fill 3s ease-out forwards 2.5s;
  }

  /* 加载点样式 - 与welcome_screen.py一致 */
  .loading-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    opacity: 0;
    animation: fade-in 1s ease-out forwards 2.3s;
  }

  .loading-text {
    font-size: 16px;
    color: var(--blue-grey-600);
    margin-right: 10px;
  }

  .dot {
    width: 8px;
    height: 8px;
    background: var(--primary-blue);
    border-radius: 50%;
    opacity: 0.4;
    animation: dot-blink 0.8s ease-in-out infinite;
  }

  .dot:nth-child(2) { animation-delay: 0.2s; }
  .dot:nth-child(3) { animation-delay: 0.4s; }
  .dot:nth-child(4) { animation-delay: 0.6s; }

  /* 动画定义 */
  @keyframes logo-entrance {
    to {
      opacity: 1;
      transform: scale(1.0);
    }
  }

  @keyframes pulse-ring {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.08); }
  }

  @keyframes fade-in {
    to { opacity: 1; }
  }

  @keyframes progress-fill {
    to { width: 100%; }
  }

  @keyframes dot-blink {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 1.0; }
  }

  @keyframes scan-move {
    0% {
      top: 0;
      left: 0;
      width: 100vw;
    }
    100% {
      top: 100vh;
      left: 0;
      width: 100vw;
    }
  }

  @keyframes float-particle {
    from {
      transform: translateY(100vh) scale(0);
      opacity: 1;
    }
    to {
      transform: translateY(-100vh) scale(1);
      opacity: 0;
    }
  }

  @keyframes circuit-glow {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 0.8; }
  }

  @keyframes horizontal-sweep {
    0% {
      transform: translateY(-100vh);
      opacity: 0;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      transform: translateY(100vh);
      opacity: 0;
    }
  }

  @keyframes vertical-sweep {
    0% {
      transform: translateX(-100vw);
      opacity: 0;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      transform: translateX(100vw);
      opacity: 0;
    }
  }

  @keyframes icon-pulse {
    0%, 100% {
      transform: scale(1);
      text-shadow: 0 0 10px rgba(255,255,255,0.5);
    }
    50% {
      transform: scale(1.1);
      text-shadow: 0 0 20px rgba(255,255,255,0.8);
    }
  }

  /* 页面切换动画 */
  .fade-out {
    animation: fade-out-animation 0.5s ease-in forwards;
  }

  @keyframes fade-out-animation {
    to {
      opacity: 0;
      transform: scale(0.95);
    }
  }
</style>
</head>
<body>

<div class="background-container" id="backgroundContainer">
  <!-- 扫描线效果 -->
  <div class="scan-line"></div>
</div>
<div class="scene" id="mainScene">

  <!-- 主要内容容器 -->
  <div class="loader-container">
    <!-- Logo容器 - 财务机器人主题 -->
    <div class="logo-wrapper">
      <div class="logo-outer"></div>
      <div class="logo-middle"></div>
      <div class="logo-inner" id="logoInner">
        <!-- 默认显示图标，如果rpa.ico加载成功则替换 -->
        <div class="logo-icon" id="logoIcon">🤖</div>
        <div class="logo-text">财务<br>机器人</div>
      </div>
    </div>

    <!-- 标题文本 - 与welcome_screen.py保持一致 -->
    <div class="title">信小财 RPA 财务机器人</div>
    <div class="subtitle">企业隐形数字员工 • 安全可靠 • 成本效益高</div>

    <!-- 进度条 -->
    <div class="progress-container">
      <div class="progress-bar"></div>
    </div>

    <!-- 加载文本和点 -->
    <div class="loading-dots">
      <span class="loading-text">正在启动</span>
      <div class="dot"></div>
      <div class="dot"></div>
      <div class="dot"></div>
    </div>
  </div>
</div>

<script>
  // 动态创建粒子效果 - 与welcome_screen.py保持一致
  const background = document.getElementById('backgroundContainer');
  const numParticles = 20; // 与welcome_screen.py一致的粒子数量

  for (let i = 0; i < numParticles; i++) {
    let particle = document.createElement('div');
    particle.className = 'particle';
    let size = 4; // 与welcome_screen.py一致的粒子大小
    particle.style.width = size + 'px';
    particle.style.height = size + 'px';
    particle.style.top = Math.random() * 100 + 'vh';
    particle.style.left = Math.random() * 100 + 'vw';
    particle.style.animationDuration = (Math.random() * 10 + 15) + 's';
    particle.style.animationDelay = Math.random() * -20 + 's';
    background.appendChild(particle);
  }

  // 创建电路线条效果 - 模拟welcome_screen.py的circuit_lines，扩展到全屏范围
  const numLines = 15; // 增加线条数量以更好覆盖屏幕
  for (let i = 0; i < numLines; i++) {
    let line = document.createElement('div');
    line.className = 'circuit-line';

    // 扩展线条生成范围到整个屏幕，包括边缘区域
    // 扩展线条生成范围，覆盖屏幕边缘
    let startX = Math.random() * window.innerWidth * 1.2 - window.innerWidth * 0.1;
    let startY = Math.random() * window.innerHeight * 1.2 - window.innerHeight * 0.1;
    let endX = startX + (Math.random() - 0.5) * 500; // 增加线条长度
    let endY = startY + (Math.random() - 0.5) * 500; // 增加垂直变化范围

    // 允许线条延伸到屏幕边界外，创造更自然的效果
    endX = Math.max(-100, Math.min(endX, window.innerWidth + 100));
    endY = Math.max(-100, Math.min(endY, window.innerHeight + 100));

    let width = Math.abs(endX - startX);
    let height = Math.abs(endY - startY);

    line.style.width = Math.max(width, 2) + 'px';
    line.style.height = Math.max(height, 2) + 'px';
    line.style.left = Math.min(startX, endX) + 'px';
    line.style.top = Math.min(startY, endY) + 'px';
    line.style.animationDelay = Math.random() * 4 + 's'; // 增加动画延迟范围

    background.appendChild(line);
  }

  // 创建水平扫描线 - 确保覆盖整个屏幕宽度
  const numHorizontalLines = 3;
  for (let i = 0; i < numHorizontalLines; i++) {
    let hLine = document.createElement('div');
    hLine.className = 'horizontal-line';
    hLine.style.top = Math.random() * window.innerHeight + 'px';
    hLine.style.left = '0px'; // 确保从屏幕最左边开始
    hLine.style.animationDelay = Math.random() * 4 + 's';
    hLine.style.animationDuration = (4 + Math.random() * 2) + 's';
    background.appendChild(hLine);
  }

  // 创建垂直扫描线
  const numVerticalLines = 2;
  for (let i = 0; i < numVerticalLines; i++) {
    let vLine = document.createElement('div');
    vLine.className = 'vertical-line';
    vLine.style.left = Math.random() * window.innerWidth + 'px';
    vLine.style.top = '0px'; // 确保从屏幕最顶部开始
    vLine.style.animationDelay = Math.random() * 5 + 's';
    vLine.style.animationDuration = (5 + Math.random() * 2) + 's';
    background.appendChild(vLine);
  }

  // 页面切换动画函数
  function fadeOutAndRedirect(url) {
    const mainScene = document.getElementById('mainScene');
    mainScene.classList.add('fade-out');

    setTimeout(() => {
      window.location.href = url;
    }, 500);
  }

  // 加载rpa.ico图标 - 直接使用 pywebview API
  function loadRpaIcon() {
    console.log('🔍 开始检查 pywebview API...');
    console.log('window.pywebview:', window.pywebview);
    console.log('window.pywebview.api:', window.pywebview ? window.pywebview.api : 'undefined');

    // 直接尝试通过 pywebview API 获取图标数据
    if (window.pywebview && window.pywebview.api && window.pywebview.api.get_icon_data) {
      console.log('✅ pywebview API 可用，开始加载图标...');

      window.pywebview.api.get_icon_data().then(function(iconData) {
        if (iconData) {
          console.log('✅ 成功获取图标数据，长度:', iconData.length);
          createIconImage(iconData);
        } else {
          console.log('❌ API 返回空数据，保持 emoji');
        }
      }).catch(function(error) {
        console.log('❌ API 调用失败:', error);
      });
    } else {
      console.log('❌ pywebview API 不可用');
      console.log('  - window.pywebview 存在:', !!window.pywebview);
      console.log('  - window.pywebview.api 存在:', !!(window.pywebview && window.pywebview.api));
      console.log('  - get_icon_data 方法存在:', !!(window.pywebview && window.pywebview.api && window.pywebview.api.get_icon_data));
    }
  }



  // 创建图标图片元素的通用函数
  function createIconImage(src) {
    const logoIcon = document.getElementById('logoIcon');
    const logoInner = document.getElementById('logoInner');

    logoIcon.style.display = 'none';

    const iconImg = document.createElement('img');
    iconImg.src = src;
    iconImg.className = 'logo-image';
    iconImg.alt = '财务机器人';
    iconImg.style.objectFit = 'contain';
    iconImg.style.maxWidth = '120px';
    iconImg.style.maxHeight = '120px';
    iconImg.style.borderRadius = '10px';

    logoInner.insertBefore(iconImg, logoIcon);
  }

  // 页面加载完成后尝试加载图标
  document.addEventListener('DOMContentLoaded', function() {
    // 延迟一下确保 pywebview API 完全初始化
    setTimeout(loadRpaIcon, 500);
  });

  // 如果需要，可以通过这个函数从外部触发页面切换
  window.triggerPageTransition = fadeOutAndRedirect;
</script>

</body>
</html>
