#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载功能的脚本
"""

import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.base.update import update

def test_download():
    """测试下载功能"""
    print("测试下载功能...")
    
    # 测试下载一个小文件
    test_url = "https://httpbin.org/bytes/1024"  # 下载1KB的测试数据
    test_path = "test_download_file.bin"
    
    try:
        success, error = update.download_file_reliable(test_url, test_path)
        
        if success:
            print("✓ 下载测试成功")
            if os.path.exists(test_path):
                file_size = os.path.getsize(test_path)
                print(f"✓ 文件大小: {file_size} 字节")
                os.remove(test_path)  # 清理测试文件
                print("✓ 测试文件已清理")
            else:
                print("✗ 文件不存在")
        else:
            print(f"✗ 下载测试失败: {error}")
            
    except Exception as e:
        print(f"✗ 测试过程中出现异常: {e}")

if __name__ == "__main__":
    test_download()
