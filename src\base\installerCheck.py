#flet版本要在0.25.2
import sys
import subprocess
import importlib
import requests
import sys
import os
import src.base.settings as setting
class PackageInstaller:
    # 特殊包映射：安装名 -> 导入名
    SPECIAL_PACKAGES = {
        'pywin32': 'win32com',
        'python-docx': 'docx',
        'PyMuPDF': 'fitz',
        'pycryptodome': 'Crypto', 
        'python-calamine': 'python_calamine',
        'pillow': 'PIL',
        'pywebview': 'webview',
        # 可以继续添加其他特殊映射
    }

    @classmethod
    def install_package(cls, package):
        """
        安装指定的包，处理特殊情况
        :param package: 包名
        """
        # 获取实际导入名
        import_name = cls.SPECIAL_PACKAGES.get(package, package)

        try:
            # 尝试导入
            importlib.import_module(import_name)
            print(f"{package} 已经安装 (导入名: {import_name})")
            return True
        except ImportError:
            print(f"{package} 未安装 (导入名: {import_name})，正在安装...")
            try:
                # 使用 pip 安装
                subprocess.check_call([setting.PATH_EXE+"/runtime/pythonw.exe", '-m', 'pip', 'install', package,"-i https://mirrors.pku.edu.cn/pypi/web/simple"])
                
                # 再次尝试导入
                importlib.import_module(import_name)
                print(f"{package} 安装成功 (导入名: {import_name})")
                return True
            except (subprocess.CalledProcessError, ImportError) as e:
                print(f"{package} 安装失败: {str(e)}")
                return False

    @classmethod
    def check_and_install_requirements(cls, requirements_list):
        """
        检查并安装所需的库
        :param requirements_list: 需要检查的库列表
        :return: 成功安装的包列表
        """
        installed_packages = []
        for package in requirements_list:
            if cls.install_package(package):
                installed_packages.append(package)
        return installed_packages

def main():
    #先卸载playwright再重新安装
    print("卸载playwright")
    subprocess.check_call([setting.PATH_EXE+"/runtime/pythonw.exe", '-m', 'pip', 'uninstall', '-y', 'playwright'])
    print("重新安装playwright")
    # 需要检查的库列表（包括特殊包）
    required_packages = [
        'numpy',
        'pandas',
        'requests',
        'pywin32',     # 映射到 win32com
        'flet',
        'openpyxl',
        'duckdb',
        'PyMuPDF',     # 映射到 fitz
        'playwright',
        'pycryptodome', # 映射到 Crypto
        'wmi',
        'excelize',
        'pillow',      # 映射到 PIL
        'python-docx', # 映射到 docx
        'python-calamine', #映射到 python_calamine
        'ollama',
        'pywebview',
        #'sentence-transformers',
        # 'faiss-cpu', 
        # 'scikit-learn',
        # 'jieba'
    ]
    
    # 获取缺失的包
    missing_packages = []
    for package in required_packages:
        # 获取实际导入名
        import_name = PackageInstaller.SPECIAL_PACKAGES.get(package, package)
        
        try:
            # 尝试导入模块
            importlib.import_module(import_name)
            print(f"✓ {package} 已安装 (导入名: {import_name})")
        except ImportError as e:
            print(f"✗ {package} 未安装 (导入名: {import_name}): {str(e)}")
            missing_packages.append(package)

    if missing_packages:
        print("\n检测到以下包未安装:")
        for package in missing_packages:
            import_name = PackageInstaller.SPECIAL_PACKAGES.get(package, package)
            print(f"- {package} (导入名: {import_name})")
        
        print("\n开始安装缺失的包...")
        installed = PackageInstaller.check_and_install_requirements(missing_packages)
        
        # 验证安装结果
        if installed:
            print("\n以下包已成功安装:")
            for package in installed:
                import_name = PackageInstaller.SPECIAL_PACKAGES.get(package, package)
                print(f"- {package} (导入名: {import_name})")
        else:
            print("\n未成功安装任何包")
    else:
        print("所有必需的包都已安装")


if __name__ == "__main__":  
    main()


