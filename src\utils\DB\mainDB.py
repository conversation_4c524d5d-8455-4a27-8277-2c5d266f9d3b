import duckdb
import src.base.settings as settings
import src.utils.DB.outputSQL as outputSQL
from datetime import datetime, timedelta
import src.utils.Excel.openpyxlExcel as openpyxlExcel
import src.utils.DB.readtxttolist as readtxttolist
import pandas as pd
import duckdb
import src.utils.DB.outputSQL as sql
import src.utils.sapPublic.sapExport as sapExport
import src.base.settings as settings
import src.base.cache as cache
from src.utils.DB.configDB import configDB
import src.utils.fileui as fileui
from src.utils.DB.calculationLedger import getLedger

class mainDB:
    def __init__(self):
        self.conn = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)
    
    def getCloseComputeByDetailLedger(self,year,month):
        first_day = datetime.strptime(f"{year}-{month}-01", "%Y-%m-%d")
        # 获取下个月的第一天
        if first_day.month == 12:
            next_month_first_day = datetime(first_day.year + 1, 1, 1)
        else:
            next_month_first_day = datetime(first_day.year, first_day.month + 1, 1)
        # 计算这个月的最后一天
        last_day = next_month_first_day - timedelta(days=1)
        # 将最后一天转换为字符串格式
        last_day_str = last_day.strftime("%Y-%m-%d")
        query=outputSQL.按利润中心计算余额明细帐结账用
        
        concatArray=configDB().internalCustomers
        if len(concatArray)==0:
            replaceCondition1="('总部客商名称')"
        elif len(concatArray)==1:
            replaceCondition1=f"('{concatArray[0][0]}')"
        else:
            concatArrayTuple=tuple(concatArray[i][0] for i in range(1,len(concatArray)))
            replaceCondition1=str(concatArrayTuple)
        query=query.replace("('总部客商名称')",replaceCondition1)

        query=query.replace("年初日期留存",f"{year}-01-01")
        query=query.replace("期末日期留存",last_day_str)
        replaceCondition2=configDB().expenseTransfer[0][0]
        query=query.replace("机关划转费用科目",replaceCondition2)
        queryParams={"年初日期":datetime(year,1,1),"期末日期":last_day}
        df0=self.conn.execute(query,queryParams).fetchall()
        title=[row[0] for row in self.conn.description]
        inputList1=[title]+df0
        query2=outputSQL.快速科目余额表
        query2=query2.replace("期初日期留",f"{year}-01-01")
        query2=query2.replace("期末日期留",last_day_str)
        df1=self.conn.execute(query2).fetchall()
        title=[row[0] for row in self.conn.description]
        inputList2=[title]+df1
        return [inputList1,inputList2]

    def getCloseDataByBalance(self):
        query=outputSQL.按利润中心计算主要科目金额
        concatArray=configDB().internalCustomers
        if len(concatArray)==0:
            replaceCondition1="('总部客商名称')"
        elif len(concatArray)==1:
            replaceCondition1=f"('{concatArray[0][0]}')"
        else:
            concatArrayTuple=tuple(concatArray[i][0] for i in range(1,len(concatArray)))
            replaceCondition1=str(concatArrayTuple)
        query=query.replace("('总部客商名称')",replaceCondition1)
        replaceCondition2=configDB().expenseTransfer[0][0]
        query=query.replace("机关划转费用科目",replaceCondition2)
        df0=self.conn.execute(query).fetchall()
        title=[row[0] for row in self.conn.description]
        inputList=[title]+df0

        query2=outputSQL.快速科目余额表余额表
        df1=self.conn.execute(query2).fetchall()
        title=[row[0] for row in self.conn.description]
        inputList2=[title]+df1
        return [inputList,inputList2]

    def close(self):
        self.conn.close()

    def importBalanceToDB (self,startDate,endDate,tableindex):
        #保存科目余额表到数据库
        print("导出科目余额表")
        if "current_year" in tableindex:
            tableindex="2"
        if "previous_year" in tableindex:
            tableindex="1"
        tableNamedict={"1":"科目余额表第一期","2":"科目余额表第二期"}
        tableName=tableNamedict[tableindex]
        pathList=sapExport.exportMain("导出科目余额表",startDate,endDate)
        accountList=[]
        for path in pathList:
            temporaryList=readtxttolist.readTxtForAccountBalTable(path)
            if len(accountList)>0:
                accountList=accountList+temporaryList[1:]
            else:
                accountList=temporaryList+accountList
        df=pd.DataFrame(accountList[1:],columns=accountList[0])
        self.conn.execute(f"DROP TABLE IF EXISTS {tableName}")
        # 根据 df 结构创建新表并插入数据
        self.conn.register('pandas_df', df)
        self.conn.execute(f"CREATE TABLE {tableName} AS SELECT * FROM pandas_df")
        df.to_excel(settings.PATH_QUERY+"/科目余额表sap导出.xlsx",index=False)
        print("科目余额表保存一份本地表在"+settings.PATH_QUERY+"/科目余额表sap导出.xlsx")

    
    def inserData(self,filePath=None):
        title0=openpyxlExcel.getUsedList("数据库所用标题")
        title=[ele[0] for ele in title0]
        if filePath==None:
            filePath=fileui.select_file(initialdir=settings.PATH_DOWNLOAD+"/导出明细数据")
        for path in filePath:
            print("开始导入数据库...")
            arr=readtxttolist.readTxt(path,title)
            df=pd.DataFrame(arr,columns=title)
            df.drop_duplicates('主键',
                        keep='first',
                        inplace=True,
                        ignore_index=True)
            self.conn.sql("INSERT OR REPLACE INTO 明细帐 SELECT * FROM df  ") # 从视图创建一个持久的表
            getLedger(self.conn,df)
            print("数据库导入完成")
        self.conn.execute(sql.科目对照)
    
    def queryDataByProject(self,profitCenterCode:str):

        arr1 =self.conn.execute(f"select * from 内部对账 where 利润中心='{profitCenterCode}'").fetchall()
        arr1 = [[row[0] for row in self.conn.description]]+arr1
        arr2 =self.conn.execute(f"select * from 专项储备 where 利润中心='{profitCenterCode}'").fetchall()
        arr2 = [[row[0] for row in self.conn.description]]+arr2
        arr3 =self.conn.execute(f"select * from 收款台账 where 利润中心='{profitCenterCode}'").fetchall()
        arr3 = [[row[0] for row in self.conn.description]]+arr3
        arr4 =self.conn.execute(f"select * from 资金整理 where 利润中心='{profitCenterCode}'").fetchall()
        arr4 = [[row[0] for row in self.conn.description]]+arr4
        arr5 =self.conn.execute(f"select * from 成本表 where 利润中心='{profitCenterCode}'").fetchall()
        arr5 = [[row[0] for row in self.conn.description]]+arr5
        arr6 =self.conn.execute(f"select * from 分供结算台账 where 利润中心='{profitCenterCode}'").fetchall()
        arr6 = [[row[0] for row in self.conn.description]]+arr6
        arr7 =self.conn.execute(f"select * from 付款台账 where 利润中心='{profitCenterCode}'").fetchall()
        arr7 = [[row[0] for row in self.conn.description]]+arr7
        arr8 =self.conn.execute(f"select * from 外部确权台账 where 利润中心='{profitCenterCode}'").fetchall()
        arr8 = [[row[0] for row in self.conn.description]]+arr8
        arr9 =self.conn.execute(f"select * from 应付汇总按合同 where 利润中心='{profitCenterCode}'").fetchall()
        arr9 = [[row[0] for row in self.conn.description]]+arr9
        arr10 =self.conn.execute(f"select * from 应付汇总按供应商 where 利润中心='{profitCenterCode}'").fetchall()
        arr10 = [[row[0] for row in self.conn.description]]+arr10


        arr0=self.conn.execute(f"select * from 总台账 where 利润中心='{profitCenterCode}'").fetchall()[0]
        title=[row[0] for row in self.conn.description]
        row={key:arr0[i] for i,key in enumerate(title)}

        return {
  "code": 200,
  "message": "success",
  "data": {
    "projectInfo": {
      "projectName": row['利润中心描述'],
      "profitCenterCode": row['利润中心']
    },
    "financialData": {
      "mainBusinessIncome": row['收入'],
      "mainBusinessCost": row['成本'],
      "additionalTax": row['附加税'],
      "cumulativeConfirmation": row['收入']-row['合同余额'],
      "cumulativeIndirectCost": row['商务间接费用'],
      "cumulativeMachineryCost": row['商务机械费用'],
      "nonSubcontractSafetyFee": row['非分包安全费'],
      "specialReserveBalance": row['专项储备余额'],
      "contractPerformanceCostBalance": row['合同履约成本余额'],
      "rawMaterialBalance": row['原材料'],
      "estimatedPayableBalance": row['暂估应付余额'],
      "totalContractSettlement": row['总包结算额'],
      "totalContractPayment": row['总包付款额'],
      "totalContractEstimate": row['总包暂估额'],
      "reconciliationCheck": 0,
      "taxPayablePendingWriteOff": row['销项税余额'],
      "invoiceAmountFromTax": 0,
      "cumulativeTaxInclusiveConfirmation":row['累计确权'],
      "cumulativeOwnerReceipt": row['累计收款'],
      "cumulativeDepositReceipt": 0,
      "cumulativeSubcontractorSettlement": row['累计分供结算'],
      "cumulativeSubcontractorPayment": row['累计分供付款'],
      "cumulativeTaxPayment": 0,
      "cumulativeManagementExpense": 0,
      "cumulativeOtherPayments": 0,
      "originalStock": row['原始存量'],
      "internalLoan": row['内部借款'],
      "factoringLoan": row['保理借款'],
      "internalTransactionHeadquarters": row['内部往来挂总部'],
      "internalTransactionAdjustment": row['内部往来需调整'],
      "realFundBalance": row['原始存量']+row['内部借款']+row['保理借款']+row['内部往来挂总部']+row['内部往来需调整']
    },
    "detailTables": {
      "payableSuppliers": arr10,
      "payableContracts": arr9,
        "paymentLedger": arr7, 
        "subcontractorSettlement": arr6,
        "costLedger": arr5,
        "fundManagement": arr4,
        "receiptLedger": arr3,
        "externalConfirmation": arr2,
        "safetyFeeLedger": arr1,
        "internalReconciliation": arr0
    }
  },
  "timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
}
        
        

def syncSapDataToDB(startDate,endDate):
    import src.utils.sapPublic.sapExport
    writepathToDB=src.utils.sapPublic.sapExport.exportMain("导出明细数据",startDate,endDate,True)
    from src.utils.DB.mainDB import mainDB
    mainDB().inserData(writepathToDB)  #将导出后数据写入数据库
    cache.wirteNowdate() 

def syncSapDataToDBByExcel():
    import src.utils.sapPublic.sapExport
    src.utils.sapPublic.sapExport.exportMain("按excel模板导出明细数据")

def wirteDataframeToDB(df: pd.DataFrame, tableName: str) -> None:
    #将数据写入数据库
    conn = mainDB().conn
    conn.execute(f"DROP TABLE IF EXISTS {tableName}")
    # 根据 df 结构创建新表并插入数据
    conn.register('pandas_df', df)
    conn.execute(f"CREATE TABLE {tableName} AS SELECT * FROM pandas_df")
    conn.close()

def openConfigExcel():
    import os
    os.startfile(settings.PATH_CONFIG+"/配置文件.xlsx")
    
def reinsertData() -> None:
    #将数据写入数据库
    db = mainDB()
    db.inserData()
    db.close()

def updateDataframeToDB(df: pd.DataFrame, tableName: str) -> None:
    #将数据写入数据库
    conn = mainDB().conn
    conn.execute(f"insert into {tableName} select * from df")
    conn.close()

def queryDistctinctColumn(tableName: str, columnName: str) -> list:
    #查询数据库中某一列的所有不重复值
    conn = mainDB().conn
    query = f"SELECT DISTINCT {columnName} FROM {tableName}"
    result = conn.execute(query).fetchall()
    conn.close()
    return result



    