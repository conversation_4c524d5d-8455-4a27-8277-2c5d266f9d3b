import sys
import os
import webview
import multiprocessing
import signal
import win32gui
import win32con
import time
import threading
def on_closing():
    """Handle window close event"""
    print("Closing application...")
    # Terminate the server process
    if 'server_process' in globals() and server_process.is_alive():
        print("Terminating server process...")
        server_process.terminate()
        server_process.join() # Wait for the process to terminate
    print("Application closed.")

def _set_icon(title, icon_path):
    """Set the window icon using win32gui after the window is created - 百分百成功版本"""
    def icon_setter():
        try:
            # 首先检查图标文件是否存在
            if not os.path.exists(icon_path):
                print(f"图标文件不存在: {icon_path}")
                return False

            # 预加载图标，避免后续加载失败
            h_icon = None
            try:
                # 尝试多种加载方式
                h_icon = win32gui.LoadImage(0, icon_path, win32con.IMAGE_ICON, 0, 0, 
                                          win32con.LR_LOADFROMFILE | win32con.LR_DEFAULTSIZE)
                if h_icon == 0:
                    # 尝试指定尺寸加载
                    h_icon = win32gui.LoadImage(0, icon_path, win32con.IMAGE_ICON, 32, 32, 
                                              win32con.LR_LOADFROMFILE)
                if h_icon == 0:
                    # 尝试16x16尺寸
                    h_icon = win32gui.LoadImage(0, icon_path, win32con.IMAGE_ICON, 16, 16, 
                                              win32con.LR_LOADFROMFILE)
            except Exception as e:
                print(f"预加载图标失败: {e}")
                return False

            if h_icon == 0:
                print(f"无法加载图标文件: {icon_path}")
                return False

            # 强化的窗口查找机制
            hwnd = 0
            attempts = 0
            max_attempts = 200  # 增加到200次尝试
            
            while attempts < max_attempts:
                # 方法1: 精确标题匹配
                hwnd = win32gui.FindWindow(None, title)
                if hwnd != 0:
                    break
                    
                # 方法2: 枚举所有窗口查找包含标题的窗口
                def enum_windows_callback(hwnd_enum, lparam):
                    nonlocal hwnd
                    try:
                        window_title = win32gui.GetWindowText(hwnd_enum)
                        if title in window_title and win32gui.IsWindowVisible(hwnd_enum):
                            hwnd = hwnd_enum
                            return False  # 停止枚举
                    except:
                        pass
                    return True
                
                if attempts > 50:  # 50次后开始使用枚举方式
                    try:
                        win32gui.EnumWindows(enum_windows_callback, None)
                        if hwnd != 0:
                            break
                    except:
                        pass
                
                attempts += 1
                time.sleep(0.1)  # 减少间隔，增加响应速度

            if hwnd == 0:
                print(f"窗口查找失败，尝试了 {attempts} 次")
                return False

            # 多重设置确保成功
            success = False
            for attempt in range(5):  # 最多尝试5次设置
                try:
                    # 设置大图标
                    result1 = win32gui.SendMessage(hwnd, win32con.WM_SETICON, win32con.ICON_BIG, h_icon)
                    # 设置小图标
                    result2 = win32gui.SendMessage(hwnd, win32con.WM_SETICON, win32con.ICON_SMALL, h_icon)
                    
                    # 验证设置是否成功
                    time.sleep(0.1)
                    current_big = win32gui.SendMessage(hwnd, win32con.WM_GETICON, win32con.ICON_BIG, 0)
                    current_small = win32gui.SendMessage(hwnd, win32con.WM_GETICON, win32con.ICON_SMALL, 0)
                    
                    if current_big != 0 or current_small != 0:
                        success = True
                        print(f"成功设置窗口图标 (尝试 {attempt + 1}/5): {icon_path}")
                        break
                    else:
                        print(f"图标设置验证失败，重试 {attempt + 1}/5")
                        time.sleep(0.2)
                        
                except Exception as e:
                    print(f"设置图标时出错 (尝试 {attempt + 1}/5): {e}")
                    time.sleep(0.2)
            
            # 额外的保险措施：强制刷新窗口
            if success:
                try:
                    win32gui.RedrawWindow(hwnd, None, None, 
                                        win32con.RDW_FRAME | win32con.RDW_INVALIDATE | win32con.RDW_UPDATENOW)
                except:
                    pass
            
            return success
            
        except Exception as e:
            print(f"图标设置过程中出现异常: {e}")
            return False
    
    # 在单独线程中执行，避免阻塞主线程
    def threaded_icon_setter():
        success = icon_setter()
        if not success:
            print("图标设置失败，将在5秒后重试...")
            time.sleep(5)
            icon_setter()  # 最后一次重试
    
    threading.Thread(target=threaded_icon_setter, daemon=True).start()

# This global variable will be assigned in the thread
server_process = None

def wait_for_server(url, max_attempts=30, delay=0.5):
    """等待服务器启动并响应"""
    import requests
    for _ in range(max_attempts):
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                return True
        except requests.exceptions.RequestException:
            pass
        time.sleep(delay)
    return False

class LoadingAPI:
    """为 loading.html 提供的 API 接口"""

    def get_icon_data(self):
        """获取图标的 base64 数据"""
        import src.base.settings as setting
        import base64

        icon_path = os.path.join(setting.CACHE_PATH, "rpa.ico")
        print(f"尝试读取图标: {icon_path}")
        try:
            with open(icon_path, 'rb') as f:
                icon_data = base64.b64encode(f.read()).decode('utf-8')
            return f"data:image/x-icon;base64,{icon_data}"
        except Exception as e:
            print(f"读取图标失败: {e}")
            return None

def load_main_app(window):
    """启动服务器并加载主应用，带有平滑的过渡动画"""
    global server_process
    print("正在启动后端服务器...")

    try:
        from src.web.http import start_server
        server_process = start_server()

        #from src.web.http import run_server
        #run_server()

        # 等待服务器启动
        server_url = "http://localhost:8000"
        print("等待服务器响应...")

        if wait_for_server(server_url):
            print("服务器启动成功，准备切换到主应用...")

            # 添加一个短暂的延迟，让loading动画完成
            time.sleep(2)

            # 使用JavaScript触发淡出动画，然后切换页面
            window.evaluate_js("""
                if (window.triggerPageTransition) {
                    window.triggerPageTransition('http://localhost:8000');
                } else {
                    window.location.href = 'http://localhost:8000';
                }
            """)
        else:
            print("服务器启动超时，直接加载主应用...")
            window.load_url(server_url)

        import src.Gui.callProcess as callProcess #导入开始创建函数进程
        from src.Gui.FletGui.register_dialog import RegisterDialog #创建注册对象
        from src.Gui.register import initLock,updateRegister
        initLock()
        updateRegister()

    except Exception as e:
        print(f"启动服务器时出错: {e}")
        # 如果出错，直接尝试加载
        window.load_url("http://localhost:8000")

def _icon_monitor(title, icon_path):
    """图标监控器 - 定期检查并确保图标正确设置"""
    def monitor():
        time.sleep(10)  # 等待10秒后开始监控
        check_count = 0
        
        while check_count < 6:  # 监控1分钟（每10秒检查一次）
            try:
                hwnd = win32gui.FindWindow(None, title)
                if hwnd != 0:
                    # 检查当前图标状态
                    current_big = win32gui.SendMessage(hwnd, win32con.WM_GETICON, win32con.ICON_BIG, 0)
                    current_small = win32gui.SendMessage(hwnd, win32con.WM_GETICON, win32con.ICON_SMALL, 0)
                    
                    if current_big == 0 and current_small == 0:
                        print("检测到图标丢失，正在重新设置...")
                        _set_icon(title, icon_path)
                    else:
                        print(f"图标状态正常 (检查 {check_count + 1}/6)")
                else:
                    print("窗口未找到，停止监控")
                    break
                    
            except Exception as e:
                print(f"图标监控出错: {e}")
            
            check_count += 1
            time.sleep(10)
    
    threading.Thread(target=monitor, daemon=True).start()

def on_download_started(window, download_item):
    """下载开始时触发，调用 EdgeWebView2 兼容的文件选择对话框"""
    print(f"开始下载: {download_item.url}")
    
    # 1. 调用 pywebview 原生保存对话框（适配 EdgeWebView2）
    # 注意：create_file_dialog 需在窗口上下文内调用
    result = window.create_file_dialog(
        webview.SAVE_DIALOG,
        directory=os.path.expanduser("~"),  # 默认打开用户主目录
        save_filename=download_item.suggested_filename  # 用服务器建议的文件名
    )
    
    # 2. 处理用户选择
    if result:
        # 用户选择了路径，设置下载保存位置
        save_path = result[0]  # result 是列表，取第一个元素
        download_item.set_path(save_path)
        print(f"文件将保存到: {save_path}")
        
        # 可选：监听下载进度
        def on_progress(progress):
            if progress == 100:
                print("下载完成！")
        download_item.on_progress += on_progress
    else:
        # 用户取消了选择，终止下载
        print("用户取消下载")
        download_item.cancel()

def main():
    import src.base.settings as setting
    # Define the path to the loading file
    loading_html_path = os.path.join(setting.PATH_INTERNAL, 'web', 'loading.html')

    # 创建 API 对象
    loading_api = LoadingAPI()

    # Create window pointing to the loading screen
    webview.settings['ALLOW_DOWNLOADS'] = True
    window = webview.create_window(
        "信小财",
        url=loading_html_path,
        width=1350,
        height=800,
        confirm_close=True,
        js_api=loading_api,  # 传递 API 对象到 JavaScript
    )
   
    # Set the close event handler
    window.events.closed += on_closing

    # Handle Ctrl+C in console
    signal.signal(signal.SIGINT, lambda s, f: on_closing())

    # Start the server and load the main app in a separate thread
    threading.Thread(target=load_main_app, args=(window,)).start()

    chinese = {
        'global.quitConfirmation': u'确定关闭?',
    }
    try:
        # webview.start's icon parameter is not effective under the webview2 engine,
        # so we use win32gui to set the icon instead.
        ico_path = os.path.join(setting.CACHE_PATH, "rpa.ico")
        dev=False
        if os.path.exists(ico_path) and dev:
            # 启动图标设置和监控
            webview.start(localization=chinese, debug=False, http_server=True, 
                         func=lambda: [_set_icon("信小财", ico_path), _icon_monitor("信小财", ico_path)])
        else:
            print("图标文件不存在，使用默认图标")
            webview.start(localization=chinese, debug=False, http_server=True)
    except KeyboardInterrupt:
        on_closing()

if __name__ == '__main__':
    multiprocessing.freeze_support()
    main()