def laborDispatchTemplate():
    conn=excelDB()
    try:
        df = pd.read_sql("SELECT * FROM 劳务派遣认领", conn.conn)
    except:
        df = pd.DataFrame(columns=["序号", "是否分割", "单位", "项目名称", "摘要","事由","附件"])
    conn.conn.close()
    return {"劳务派遣认领":[df.columns.tolist()]+df.values.tolist()}

def laborDispatchUpdate(data):
    conn=excelDB()
    data=data["currentData"]["劳务派遣认领"]
    processed_columns = [f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(data[0])]
    df = pd.DataFrame(columns=processed_columns,data=data[1:])
    df.to_sql("劳务派遣认领", conn.conn, if_exists='replace', index=False)
    conn.conn.close()


