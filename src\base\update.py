import requests
import sys
import os
import src.base.settings as setting
import subprocess
import time
import hashlib
from urllib.parse import urlparse

class update:
    def __init__(self):
        pass

    @staticmethod
    def download_file_reliable(url, local_path, max_retries=3, timeout=30, chunk_size=8192):
        """
        可靠的文件下载方法
        :param url: 下载链接
        :param local_path: 本地保存路径
        :param max_retries: 最大重试次数
        :param timeout: 超时时间（秒）
        :param chunk_size: 分块下载大小
        :return: (success, error_message)
        """
        for attempt in range(max_retries):
            try:
                print(f"正在下载 {url} (尝试 {attempt + 1}/{max_retries})")

                # 创建目录（如果不存在）
                os.makedirs(os.path.dirname(local_path), exist_ok=True)

                # 发送HEAD请求获取文件信息
                head_response = requests.head(url, timeout=timeout)
                if head_response.status_code != 200:
                    raise requests.RequestException(f"HEAD请求失败，状态码: {head_response.status_code}")

                expected_size = int(head_response.headers.get('content-length', 0))
                print(f"文件大小: {expected_size} 字节")

                # 下载文件
                response = requests.get(url, timeout=timeout, stream=True)
                response.raise_for_status()

                downloaded_size = 0
                temp_path = local_path + ".tmp"

                with open(temp_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)

                            # 显示下载进度
                            if expected_size > 0:
                                progress = (downloaded_size / expected_size) * 100
                                print(f"\r下载进度: {progress:.1f}% ({downloaded_size}/{expected_size})", end='', flush=True)

                print()  # 换行

                # 验证文件大小
                actual_size = os.path.getsize(temp_path)
                if expected_size > 0 and actual_size != expected_size:
                    os.remove(temp_path)
                    raise ValueError(f"文件大小不匹配: 期望 {expected_size}, 实际 {actual_size}")

                # 验证文件不为空
                if actual_size == 0:
                    os.remove(temp_path)
                    raise ValueError("下载的文件为空")

                # 移动临时文件到目标位置
                if os.path.exists(local_path):
                    os.remove(local_path)
                os.rename(temp_path, local_path)

                print(f"文件下载成功: {local_path}")
                return True, None

            except Exception as e:
                error_msg = f"下载失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}"
                print(error_msg)

                # 清理临时文件
                temp_path = local_path + ".tmp"
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass

                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    return False, error_msg

        return False, "所有重试都失败了"
    @staticmethod
    def downloadFile():
        print("开始更新，请等待...")
        #检测是否存在pywebview,不存在则sys.executable -m pip install pywebview -i https://mirrors.pku.edu.cn/pypi/web/simple
        try:
            import webview
        except ImportError:
            print("pywebview未安装，正在安装...")
            sysExE=setting.PATH_EXE+"/runtime/python.exe"
            try:
                subprocess.check_call([sysExE, '-m', 'pip', 'install', 'pywebview', "-i https://mirrors.pku.edu.cn/pypi/web/simple"])
            except subprocess.CalledProcessError as e:
                print("pywebview安装失败: ",e)
                return
            print("pywebview部分完成")
        url="https://cscec3b-fip.hb11oss.ctyunxs.cn/ftools.zip"
        path0=setting.PATH_EXE+"/runtime/ftools.zip"
        path1=setting.PATH_EXE+"/runtime/ftools2.zip"
        success, error = update.download_file_reliable(url, path1)
        if not success:
            print(f"下载失败: {error}")
            return
                # 下载main2.pyc

        path_main2=os.path.join(setting.PATH_EXE, 'runtime','main2.pyc')
        path_main3=os.path.join(setting.PATH_EXE, 'runtime','main3.pyc')
        #if not os.path.exists(path_main2):
        url="https://cscec3b-fip.hb11oss.ctyunxs.cn/main2.pyc"
        print("下载main2.pyc...")
        success, error = update.download_file_reliable(url, path_main3)
        if not success:
            print(f"main2.pyc下载失败: {error}")
            # 这个文件不是必需的，继续执行
        else:
            os.remove(path_main2)
            os.rename(path_main3,path_main2)
            print("main下载成功")

        # 创建必要的目录并清理旧文件
        if not os.path.exists(setting.PATH_INTERNAL+"/web/assets"):
            os.makedirs(setting.PATH_INTERNAL+"/web/assets")
        if not os.path.exists(setting.PATH_INTERNAL+"/web/js"):
            os.makedirs(setting.PATH_INTERNAL+"/web/js")


        # 下载web.zip
        url="https://cscec3b-fip.hb11oss.ctyunxs.cn/web.zip"
        path=setting.PATH_INTERNAL+"/web/web.zip"
        print("更新UI组成部分")
        success, error = update.download_file_reliable(url, path)
        if not success:
            print(f"web.zip下载失败: {error}")
            return
        
         # 清理旧的assets文件
        for file in os.listdir(setting.PATH_INTERNAL+"/web/assets"):
            try:
                os.remove(setting.PATH_INTERNAL+"/web/assets/"+file)
            except Exception as e:
                print(f"删除文件失败: {file}, 错误: {e}")

        # 清理旧的js文件
        for file in os.listdir(setting.PATH_INTERNAL+"/web/js"):
            try:
                os.remove(setting.PATH_INTERNAL+"/web/js/"+file)
            except Exception as e:
                print(f"删除文件失败: {file}, 错误: {e}")
                
        # 解压web.zip
        import zipfile
        try:
            if os.path.exists(path):
                with zipfile.ZipFile(path, 'r') as z:
                    z.extractall(setting.PATH_INTERNAL+"/web")
                os.remove(path)
                print("UI组成部分更新成功")
            else:
                print("web.zip文件不存在")
                return
        except Exception as e:
            print(f"解压web.zip失败: {e}")
            return

        # 下载信小财_new.exe
        path_exe=os.path.join(setting.PATH_EXE, '信小财_new.exe')
        if not os.path.exists(path_exe):
            url="https://cscec3b-fip.hb11oss.ctyunxs.cn/信小财_new.exe"
            print("下载信小财_new.exe...")
            success, error = update.download_file_reliable(url, path_exe)
            if not success:
                print(f"信小财_new.exe下载失败: {error}")
                # 这个文件不是必需的，继续执行
            else:
                print("信小财_new.exe下载成功")

        # 下载loading.html
        loadingHtml_path=os.path.join(setting.PATH_INTERNAL, 'web/loading.html')
        if not os.path.exists(loadingHtml_path):
            url="https://cscec3b-fip.hb11oss.ctyunxs.cn/loading.html"
            print("下载loading.html...")
            success, error = update.download_file_reliable(url, loadingHtml_path)
            if not success:
                print(f"loading.html下载失败: {error}")
                # 这个文件不是必需的，继续执行
            else:
                print("loading.html下载成功")
        # 检查关键文件是否存在，如果新版本存在则删除旧版本
        old_exe_path = os.path.join(setting.PATH_EXE, '信小财.exe')
        new_exe_path = os.path.join(setting.PATH_EXE, '信小财_new.exe')

        if (os.path.exists(loadingHtml_path) and
            os.path.exists(new_exe_path) and
            os.path.exists(old_exe_path)):
            try:
                import webview  # 确保webview可用
                print("删除旧版本执行文件...")
                os.remove(old_exe_path)
                print("旧版本执行文件已删除")
            except Exception as e:
                print(f"删除旧版本执行文件失败: {e}")

        print("更新完成，请重启程序")
