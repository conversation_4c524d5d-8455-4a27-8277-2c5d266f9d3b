with 
a as (
select 
any_value(利润中心组名称) as 利润中心组名称,
any_value(利润中心名称) as 利润中心名称,
any_value(利润中心) as 利润中心,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%可用存款%' or 科目余额表第二期.总账科目长文本 like '银行存款%'  then 期末余额 else 0 end),4) as 原始存量,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\内部借贷%'  then 期末余额 else 0 end),4) as 内部借款,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款\应付供应链融资款%'  then 期末余额 else 0 end),4) as 保理借款,
原始存量+内部借款+保理借款 as 净存量未计算保理借款,
round(sum(case when 科目余额表第二期.总账科目长文本 like '专项储备%'  then 期末余额 else 0 end),4) as 专项储备余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%合同结算%' or 科目余额表第二期.总账科目长文本 like '合同资产\工程款（已完工未结算）' or 科目余额表第二期.总账科目长文本 like '%已结算未完工%' then 期末余额 else 0 end),4) as 合同余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '合同履约成本%'  then 期末余额 else 0 end),4) as 成本余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '研发支出%'  then 期末余额 else 0 end),4) as 研发支出,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%本利润中心%'  then 期末余额 else 0 end),4) as 本利润中心余额,
round(sum(case when (科目余额表第二期.总账科目长文本 like '应收账款%' or 科目余额表第二期.总账科目长文本 like '合同资产%质保金') and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年确权额,
round(sum(case when (科目余额表第二期.总账科目长文本 like '应收账款%' or 科目余额表第二期.总账科目长文本 like '合同资产%质保金') and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年收款额,
from 科目余额表第二期
GROUP by  利润中心),
b as (
select
any_value(利润中心) as 利润中心,
round(sum(case when (总账科目长文本 like '应收账款%' or 总账科目长文本 like '合同资产%质保金') and 总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年确权额,
round(sum(case when (总账科目长文本 like '应收账款%' or 总账科目长文本 like '合同资产%质保金') and 总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年收款额,
from 科目余额表第一期
GROUP by 利润中心
),
c as (
select 
利润中心组名称,利润中心名称,a.利润中心,原始存量,内部借款,保理借款,净存量未计算保理借款,专项储备余额,合同余额,成本余额,研发支出,
a.本年确权额+b.本年确权额 as 累计确权,
a.本年收款额+b.本年收款额 as 累计收款,
from a left JOIN b on a.利润中心 = b.利润中心
),
d as (select sum(结算额) as 总包结算额,sum(付款额) as 总包付款额,sum(暂估额) as 总包暂估额,any_value(利润中心) AS 利润中心 from 内部对账 GROUP by 利润中心)
select *,c.累计确权-d.总包结算额 as 结算差额,c.累计收款-d.总包付款额 as 付款差额 from c left join d on c.利润中心=d.利润中心