import time
import src.utils.Browser.Browser as browser
import src.utils.cscec as cscec
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd

def main():
    conn=excelDB()
    df=conn.getDataframe("小额税金调整")
    page=browser.myBrowser("cscec").page
    for i,row in df.iterrows():
        if row["是否"]=="是":
            conn.updateData("小额税金调整",i+1,"是否","开始执行")
            cscec.toFunction(page,"报账系统","总账业务","总账通用工单")
            table1=cscec.cscecTable(page,"科目编号")
            table1.appendRow()
            table1.clickInputQuery(1,"*科目编号")
            cscec.dialogInput(page,"其他应收款\待确认进项税额")
            table1.clickInputQuery(1,"*借贷方向")
            cscec.getVisible(page,"//div/div[text()='借方']").click()

            table1.appendRow()
            table1.clickInputQuery(2,"*科目编号")
            cscec.dialogInput(page,"合同履约成本\工程施工成本\直接材料费")
            table1.clickInputQuery(2,"*借贷方向")
            cscec.getVisible(page,"//div/div[text()='借方']").click()

            table1.click(1,"*科目名称")
            table2=cscec.cscecTable(page,"客商")
            table2.appendRow()
            time.sleep(0.2)
            table2.reIndex()
            table2.clickInputQuery(1,"*客商")
            cscec.dialogInput2(page,row["客商编码"])
            table2.clickInputQuery(1,"*增值税率")
            cscec.dialogInput(page,row["增值税描述"])
            table2.fillInput(1,"*金额",row["金额"])

            table1.click(2,"*科目名称")
            table2=cscec.cscecTable(page,"项目")
            table2.appendRow()
            time.sleep(0.2)
            table2.reIndex()
            
            if pd.isna(row["材料大类编码"]):
                table2.clickInputQuery(1,"*产品物料大类") #只有材料需要填
                cscec.dialogInput(page,"原材料\建筑业主材\机电材料及设备")
            
            table2.fillInput(1,"*金额",row["金额"])
            cscec.chooseIfPaper(page,False)
            cscec.fillReason(page,row["事由"])
            page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
            page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
            page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
            #page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='提交']").click()
            page.locator("//*[text()='处理意见']/parent::div/parent::div/parent::div//div[text()='确定']").click()
            page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
            cscec.closePage(page)
            conn.updateData("小额税金调整",i+1,"是否","完成")

def queryData(ifFile=False):
    conn=excelDB()
    if ifFile:
        try:
            df=pd.read_sql("SELECT * FROM 小额税金调整",conn.conn)
            df.fillna("",inplace=True)
        except:
            df=pd.DataFrame(columns=["单位","项目","客商编码","金额","增值税描述","事由","材料大类编码","是否"],data=[["中建三局数字","测试项目","123133","1000000","进项税3%","测试","1000000","是否"]])
        return [df.columns.tolist()]+df.values.tolist() 
    conn.queryData("小额税金调整",["单位","项目","客商编码","金额","增值税描述","事由","材料大类编码","是否"],["中建三局数字","测试项目","123133","1000000","进项税3%","测试","1000000","是否"])
    conn.close()

def writeData(data=None):
    conn=excelDB()
    if data is not None:
        title=data[0]
        processTitle=[f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(title)]
        df=pd.DataFrame(columns=processTitle,data=data[1:])
        df.to_sql("小额税金调整",conn.conn,if_exists="replace",index=False)
        return
    conn.writeExcel("小额税金调整")
    conn.close()
