import winreg
import xml.etree.ElementTree as ET

def get_sap_landscape_file():
    # 定义基础路径
    base_path = r"SOFTWARE\SAP\SAPLogon"
    
    try:
        # 打开基础路径的注册表项
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, base_path) as base_key:
            index = 0
            # 遍历所有子项
            while True:
                try:
                    # 枚举子项
                    subkey_name = winreg.EnumKey(base_key, index)
                    index += 1
                    
                    # 检查子项是否以"LandscapeFilesInUse"开头
                    if subkey_name.startswith("LandscapeFilesInUse"):
                        # 构建完整路径
                        full_path = f"{base_path}\\{subkey_name}"
                        
                        try:
                            # 打开子项
                            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, full_path) as sub_key:
                                try:
                                    # 尝试获取LandscapeFile的值
                                    value, reg_type = winreg.QueryValueEx(sub_key, "LandscapeFile")
                                    # 返回值（已自动排除REG_SZ类型信息）
                                    return value
                                except FileNotFoundError:
                                    # 如果该子项中没有LandscapeFile值，继续查找下一个
                                    continue
                        except Exception as e:
                            print(f"打开子项 {full_path} 时出错: {e}")
                except OSError:
                    # 枚举完毕，没有更多子项
                    break
        
        # 如果没有找到匹配的项
        print("未找到符合条件的LandscapeFile值")
        return None
        
    except Exception as e:
        print(f"操作注册表时出错: {e}")
        return None

if __name__ == "__main__":
    landscape_file = get_sap_landscape_file()
    if landscape_file:
        print(f"找到LandscapeFile值: {landscape_file}")
    else:
        print("未找到LandscapeFile值")
    #读取landscape_file文件内容
    with open(landscape_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print(content)
    root = ET.fromstring(content)
    # 查找所有Service元素并提取name属性
    service_names = []
    for service in root.findall('.//Services/Service'):
        name = service.get('name')
        if name:
            service_names.append(name)
    print(service_names)