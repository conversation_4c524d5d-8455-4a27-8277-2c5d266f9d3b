
from playwright.sync_api import Playwright, sync_playwright,Page
import src.utils.cscec as cscec
from src.utils.DB.midIntrSQLiteDB import excelDB
import src.utils.fileui as fileui
import time
import pandas as pd
from src.utils.DB.configDB import configDB


def queryVoucherCount(year,month,toFile=True):
    import calendar
    # 获取当月的最后一天
    last_day = calendar.monthrange(int(year), int(month))[1]
    import src.utils.DB.mainDB as mainDB
    db=mainDB.mainDB()
    df=db.conn.execute(''' select any_value(明细帐.利润中心) as 利润中心编码,any_value(明细帐.利润中心描述) as 利润中心名称,
any_value(利润中心组描述) as 利润中心组描述,
'组织编码需要手动填下，sap无此数据' as 组织编码,
count(DISTINCT 凭证编号) as 凭证数量,
'是' as 是否                   
from 明细帐
left join 主数据 on 明细帐.利润中心 = 主数据.利润中心
where 过帐日期 BETWEEN STRPTIME(?, '%Y-%m-%d') and STRPTIME(?, '%Y-%m-%d') 
GROUP BY 明细帐.利润中心 ''',(f"{year}-{month}-01",f"{year}-{month}-{last_day}")).df()
    db.close()
    unitMapping=configDB().unitMapping
    if len(unitMapping)>0:
        unitdf=pd.DataFrame(columns=["sourceUnitName","sourceUnitCode","targetUnitName"],data=unitMapping)
        df=df.merge(unitdf,left_on="利润中心组描述",right_on="sourceUnitName",how="left")
        df.drop(["组织编码"],axis=1,inplace=True)
        df.rename(columns={"sourceUnitCode":"组织编码","targetUnitName":"匹配单位名称","sourceUnitName":"目标单位名称"},inplace=True)
        df.fillna("",inplace=True)
    if not toFile:
        return {"档案成册管理":[df.columns.tolist()]+df.values.tolist()}
    file_dir = fileui.select_directory()
    df.to_excel(file_dir + "/凭证数量.xlsx", index=False)

def uploadFile(data=None):
    conn=excelDB()
    if data:
        #修正空白列
        processed_columns = [f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(data[0])]
        df=pd.DataFrame(columns=processed_columns,data=data[1:])
        df.to_sql("档案成册管理", conn.conn, if_exists='replace', index=False)
        conn.conn.close()
        return
    file_path = fileui.select_file()
    if file_path:
        df = pd.read_excel(file_path)
        df.to_sql("档案成册管理", conn.conn, if_exists='replace', index=False)
        conn.conn.close()

def downloadFile(toFile=True):
    conn=excelDB()
    try:
        df = pd.read_sql("SELECT * FROM 档案成册管理", conn.conn)
    except:
        df = pd.DataFrame(columns=["是否", "组织编码", "利润中心编码", "利润中心名称", "利润中心组描述"])
    conn.conn.close()
    if toFile:
        file_dir = fileui.select_directory()
        df.to_excel(file_dir + "/档案成册管理.xlsx", index=False)
    else:
        return {"档案成册管理":[df.columns.tolist()]+df.values.tolist()}
    
def main(year,month):
        import src.utils.Browser.Browser as Browser
        B=Browser.myBrowser("cscec")
        page=B.page
        cscec.toFunction(page,"档案系统","电子档案管理(新)","电子会计凭证收集")
        cscec.getVisible(page,"//label[text()='会计期间：']/parent::div/following-sibling::div//input/following-sibling::div").click()
        page.locator(f"//td/a[text()='{year}']").click()
        page.locator(f"//td/a[text()='{month}月']").click()
        cscec.getVisible(page,"//div[text()='确定']").click()
        conn=excelDB()
        df= pd.read_sql("SELECT * FROM 档案成册管理", conn.conn)
   
        print("共有"+str(df.index.size)+"条数据")
        for i in range(df.index.size):
            print("正在处理第"+str(i+1)+"条数据")
            if df.loc[i,"是否"]=="是":
                conn.updateData("档案成册管理","是否","本行执行中",i+1)
                cscec.clickLabel(page,"组织机构：")
                cscec.dialogInput(page,df.loc[i,"组织编码"])
                cscec.Lablechoose(page,"利润中心：","编号/名称","利润中心",df.loc[i,"利润中心编码"])
                cscec.getVisible(page,"//span[text()='查询']").click()
                cscec.getVisible(page,f"//td/div[text()='{df.loc[i,"利润中心编码"]}']")
                cscec.getVisible(page,"//span[text()='同步流水号']").click()
                cscec.getVisible(page,"//td/div[text()='000001']")
                cscec.clickDigalog(page,"系统提示")
                cscec.getVisible(page,"//span[text()='确认整理完成']").click()
                cscec.clickDigalog(page,"系统提示")
                try:
                    cscec.clickDigalog(page,"录入册信息",timeWait=30)
                    cscec.clickDigalog(page,"系统提示","否")
                except:
                    cscec.getVisible(page,"//span[text()='档案成册']").click()
                    cscec.clickDigalog(page,"系统提示")
                    cscec.clickDigalog(page,"系统提示")
                    cscec.clickDigalog(page,"系统提示")
                print("档案成册+1")
                conn.conn.execute("UPDATE 档案成册管理 SET 是否='本行执行完毕' where rowid=?",(i+1,))
                conn.conn.commit()
                time.sleep(1)
        conn.conn.close()  