import src.utils.dop as dop
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import src.utils.Excel.openpyxlExcel as openpyxlExcel
from playwright.sync_api import Playwright, sync_playwright,Page
import src.base.settings as settings
from datetime import datetime
import datetime as date
import time
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd
from src.utils.DB.configDB import configDB


def queryData():
    db=excelDB()
    headers = ['序号','是否填入', '项目编号', '项目名称','合同编号','金额','季度',"期间","资金主管","财务经理","上一级财务经理","上一级总会","是否历史项目","累计结算","累计应付","累计已付","累计欠付","比例","结果"]
    datarows=[1,'是',"123456789","测试项目","中建三局12345678",1000,"2025年2季度","2025年03月","杨主管","杨经理","周经理","周总会","否",1000,800,600,200,0.8]
    db.queryData("财商特殊支付",headers,datarows)
    db.close()

def writeData():
    db=excelDB()
    db.writeExcel("财商特殊支付")

def getTamplate():
    db=excelDB()
    headers = ['序号','是否填入', '项目编号', '项目名称','合同编号','金额','季度',"期间","资金主管","财务经理","上一级财务经理","上一级总会","是否历史项目","累计结算","累计应付","累计已付","累计欠付","比例","结果"]
    datarows=[1,'是',"123456789","测试项目","中建三局12345678",1000,"2025年2季度","2025年03月","杨主管","杨经理","周经理","周总会","否",1000,800,600,200,0.1,100]
    try:
        df = pd.read_sql("SELECT * FROM 财商特殊支付", db.conn)
        df.fillna("", inplace=True)
    except:
        df = pd.DataFrame(columns=headers,data=[datarows])
    db.close()   
    return {"财商特殊支付":[df.columns.tolist()]+df.values.tolist()}

def updateTamplate(data):
    db=excelDB()
    data=data["currentData"]["财商特殊支付"]
    processed_columns = [f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(data[0])]
    df = pd.DataFrame(columns=processed_columns,data=data[1:])
    df.to_sql("财商特殊支付", db.conn, if_exists='replace', index=False)
    db.close()

def main():
    """
    批量提交特殊支付
    
    """
    db=excelDB()
    dopUser=configDB().fqUserSettings
    B=browser.myBrowser("3b",dopUser["username"],dopUser["password"])
    page=B.page
    tryCount=10
    while tryCount>0:
        try:
            autoFill(db,page)
            tryCount=-1
        except Exception as e:
            page.close()
            page=B.reGo3b()
            print(e)
            tryCount=tryCount-1

def autoFill(db:excelDB,page:Page):
    df=db.getDataframe("财商特殊支付")
    dop.goFund(page,False)
    for i,row in df.iterrows():
        if row["是否填入"]=="是" or row["是否填入"]=="处理中":
            print(f"正在处理第{i+1}条数据")
            db.updateData("财商特殊支付","是否填入","处理中",i+1)
            project_code=row["项目编号"]
            dop.fundChooseProject(page,project_code)
            dop.goFundSpecialPayment(page)
            page.locator("//div[contains(text(),'金收支管理 / 资金支付申请 / 特殊支付申请')]/parent::div/following-sibling::div[2]//span[text()='新 增']").click()
            frame1=page.frame_locator("//*[@id='portal-form-list-container']/div[4]/iframe")
            frame1.locator("//*[contains(text(),'财商申请期间')]/parent::div/following-sibling::div[1]//input").click()
            quarter=row["季度"]
            if isinstance(quarter,datetime):
                quarter=str(quarter.year)+"年"+str(quarter.month)+"季度"
            frame1.locator(f"//span[@title='{quarter}']/preceding-sibling::span[1]").click()
            month=row["期间"]
            if isinstance(month,datetime):
                month=month.strftime("%Y年%m月")
            if isinstance(month,int):
                base_date=date.date(1900, 1, 1)
                month = (base_date + date.timedelta(days=month)).strftime("%Y年%m月")
            cscec.getVisible(frame1,f"//span[@title='{month}']").click()
            frame1.locator("//div[contains(text(),'期间')]/parent::div/parent::div//span[contains(text(),'确 定')]").click()
            frame1.locator("//*[contains(text(),'分供方合同编码')]/parent::div/following-sibling::div[1]//input[@placeholder='请选择']").click()
            frame1.locator("//div[@id='contractCode']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").fill(row["合同编号"])
            frame1.locator("//div[@class='ant-modal-body']//button[2]").click()
            frame1.locator("//div[@class='sheet']//div[1]//div[2]//div[1]//div[1]//div[1]//div[1]//div[1][contains(text(),'"+row["合同编号"]+"')]").click()
            #frame1.get_by_text("中建三局020020230045060056").click() #注意编码
            frame1.locator("//div[@class='ant-modal-footer']//button[2]").dblclick()
            #合同选择
            # 
            frame1.locator("//*[contains(text(),'特殊支付种类')]/parent::div/following-sibling::div[1]").click() #注意默认为预付款
            frame1.get_by_text("其他",exact=True).click()  

            if row["是否历史项目"]=="是":
                frame1.locator("//*[contains(text(),'累计月度结算:')]/parent::div/following-sibling::div[1]//input").fill(str(row["累计结算"]))
                frame1.locator("//*[contains(text(),'累计应付款:')]/parent::div/following-sibling::div[1]//input").fill(str(row["累计应付"]))
                frame1.locator("//*[contains(text(),'累计已付款:')]/parent::div/following-sibling::div[1]//input").fill(str(row["累计已付"]))
                frame1.locator("//*[contains(text(),'至本月合同实际欠款:')]/parent::div/following-sibling::div[1]//input").fill(str(row["累计欠付"]))

            s="//*[contains(text(),'累计月度结算:')]/parent::div/following-sibling::div[1]//input"
            valueMoney=cscec.getVisible(frame1,s).input_value()
            tk=3
            try:
                valueMoney=float(valueMoney)
            except:
                valueMoney=0
            while valueMoney<=0 and tk>0 :
                s="//*[contains(text(),'累计月度结算:')]/parent::div/following-sibling::div[1]//input"
                valueMoney=cscec.getVisible(frame1,s).input_value()
                try:
                    valueMoney=float(valueMoney)
                except:
                    valueMoney=0
                time.sleep(1)
                tk=tk-1


        
            s="//*[contains(text(),'款项类型:')]/parent::div/following-sibling::div[1]//div[@title='进度款']"
            #cscec.getVisible(frame1,s).click()  已经默认是进度款不用选择
            #frame1.get_by_text("进度款",exact=True).click()  

            frame1.locator("//*[contains(text(),'本次支付')]/parent::div/following-sibling::div[1]//input").fill(str(row["金额"]))

            frame1.locator("//*[@title='资金主管']/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
            funderManager=row["资金主管"] if isinstance(row["资金主管"],str) else str(int(row["资金主管"]))
            frame1.locator("//input[@placeholder='搜索组织、姓名']").fill(funderManager)
            frame1.locator("//i[@aria-label='图标: search']").click()
            try:
                cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()
            except:
                frame1.locator("//i[@aria-label='图标: search']").click()
                cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

            frame1.locator("//*[@title='财务经理']/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
            funacialManager=row["财务经理"] if isinstance(row["财务经理"],str) else str(int(row["财务经理"]))
            frame1.locator("//input[@placeholder='搜索组织、姓名']").nth(1).fill(funacialManager)
            frame1.locator("//i[@aria-label='图标: search']").nth(1).click()
            try:
                cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()
            except:
                frame1.locator("//i[@aria-label='图标: search']").click()
                cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

            if row["上一级财务经理"]!='' and (not pd.isna(row["上一级财务经理"])) :
                frame1.locator("//*[contains(@title,'上一级财务经理')]/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
                upperFunderManager=row["上一级财务经理"] if isinstance(row["上一级财务经理"],str) else str(int(row["上一级财务经理"]))
                frame1.locator("//input[@placeholder='搜索组织、姓名']").nth(2).fill(upperFunderManager)
                frame1.locator("//i[@aria-label='图标: search']").nth(2).click()
                try:
                    cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                    cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()
                except:
                    frame1.locator("//i[@aria-label='图标: search']").click()
                    cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                    cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

            if row["上一级总会"]!='' and (not pd.isna(row["上一级总会"])) :
                frame1.locator("//*[contains(@title,'上一级总会计师')]/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
                upperMasterAccountant=row["上一级总会"] if isinstance(row["上一级总会"],str) else str(int(row["上一级总会"]))
                frame1.locator("//input[@placeholder='搜索组织、姓名']").nth(3).fill(upperMasterAccountant)
                frame1.locator("//i[@aria-label='图标: search']").nth(3).click()
                try:
                    cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                    cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()
                except:
                    frame1.locator("//i[@aria-label='图标: search']").click()
                    cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                    cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

            s="//*[contains(text(),'截至目前项目资金账户余额:')]/parent::div/following-sibling::div[1]//input"
            valueMoney=cscec.getVisible(frame1,s).input_value()
            if float(valueMoney)<0:
                s="//*[contains(text(),'预计收款:')]/parent::div/following-sibling::div[1]//input"
                valueMoney=cscec.getVisible(frame1,s).fill(str(-float(valueMoney)+100))

            frame1.locator("//span[text()='提 交']/parent::button").click()
            db.updateData("财商特殊支付","是否填入","已处理",i+1)
    db.close()   

