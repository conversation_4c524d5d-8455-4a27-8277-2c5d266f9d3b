import sys
import os
import time
import re
sys.path.append(".")
import src.utils.Excel
import src.utils.sapPublic
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.Excel.openpyxlExcel
import src.utils.Browser.Browser as myBrowser
import src.utils.dopReport.财商自动同步应收账款台账
import src.utils.fipOther.财务一体化自动物资结算2
import src.utils.DB.creatDuckdbTable
import src.utils.dopReport.moneyIOLedger
import src.utils.fundCapital.财务一体化自动下载资金计划
import duckdb
import src.base.settings as settings
import src.utils.DB.outputSQL  as Sql
import pandas as pd
import src.utils.sapPublic.sapExport
import tkinter.filedialog
import src.utils.DB.readtxttolist
import src.utils.closeAccountingPeriod.obtainData
import src.utils.dopReport.collect
import src.utils.fundCapital.财务一体化自动调整比例
from src.utils.sapPublic.GetSAPSession import creatSAP
import subprocess
from src.utils.financialStatement.complete import complete
from src.utils.dopReport.compareData import main
from src.utils.fundCapital.财务一体化自动下载资金计划 import cachePlan
from src.utils.fundCapital.财务一体化资金计划上报 import planUpload
from src.utils.DB.midIntrSQLiteDB import excelDB
from playwright.sync_api import Playwright, sync_playwright,Page,Locator
def test():
    import multiprocessing 
    multiprocessing.freeze_support()
    from src.web.http import start_server
    process=start_server()
    import src.Gui.callProcess as callProcess #导入开始创建函数进程
    from src.Gui.FletGui.register_dialog import RegisterDialog #创建注册对象
    from src.Gui.register import initLock
    initLock()
    print("服务器启动成功")
    process.join()

def test2():
    conn=excelDB().conn
    batch="追加计划第一次"
    df0=pd.read_sql("SELECT * FROM 计划上传总表",conn)
    df1=pd.read_sql("SELECT * FROM 计划上传明细",conn)
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page=cscec.switch_to_page(default_context,"中国建筑")
        cscec.toFunction(page,"司库系统","资金预算","明细财务计划编制")
        page.locator("//span[contains(text(),'-')]/parent::div/parent::div/parent::td/div[1]").first.click()#起等待作用
        tdNext=page.locator("//span[contains(text(),'-')]/parent::div/parent::div/parent::td/div")
        #点击展开，使用递归将树形全部展开
        def clickOrg(tdNext:Locator):
            loopCount=tdNext.count()
            for orgI in range(loopCount):
                if 'QxYMABAKUSJzQ00EBnAAAAAElFTkSuQmCC' in tdNext.nth(orgI).locator("xpath=/div[1]/img[3]").get_attribute("style"):
                    tdNext.nth(orgI).locator("xpath=/div[1]/img[2]").click()
                    time.sleep(0.5)
                    tdNexts2=tdNext.nth(orgI).locator("xpath=/div[2]/div")
                    clickOrg(tdNexts2)
        clickOrg(tdNext)
        for index, row in df0.iterrows():
            print("持续运行")
            if row["是否上报"]=="是":
                df2=df1[df1['项目名称']==row['项目名称']]
                pay_dict = dict(zip(df2['合同编号'], df2['本次付款金额']))
                remarks_dict = dict(zip(df2['合同编号'], df2['备注']))
                project_name=row["项目名称"]
                project_unit=row["组织机构名称"]
                collection=row["本次收款"]
                batch=batch
                project_code=row["项目编号"]
                mainRemark=row["总备注"]
                planUpload()._fillOneProject(page,pay_dict,project_name,project_unit,collection,batch,remarks_dict,mainRemark)
def test3():
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page=cscec.switch_to_page(default_context,"中国建筑")
        js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']")
        Parameter=[]
        planDict={"中建三局020020230397990004":100000,"中建三局020020230397990002":200000,"中建三局020020230397990003":300000}
        dRemark={"中建三局020020230397990004":"测试1","中建三局020020230397990002":"测试1","中建三局020020230397990003":"测试1"}
        Parameter.append(planDict)
        Parameter.append(dRemark)
        totalPayment=js_need.evaluate(planUpload().st,Parameter)

if __name__ == '__main__':
    test()


    


    






