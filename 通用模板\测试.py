import sys
import requests
sys.path.append(".")

response = requests.get('https://cscec3b-fip.hb11oss.ctyunxs.cn/version.txt')
if response.status_code == 200:
    content = response.content.decode('utf-8')
    items = content.replace('\r\n', '\n').split('\n')
    # 解析为字典
    result = {}
    for item in items:
        # 按等号分割，只分割一次
        key, value = item.split('=', 1)
        result[key] = value
    print(result)
