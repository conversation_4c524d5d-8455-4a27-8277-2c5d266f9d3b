# pywebview 图标传递解决方案

## 问题描述

在 pywebview 环境中，loading.html 无法通过常规的 HTTP 路径（如 `/cache/rpa.ico`）访问本地文件，导致图标加载失败。

## 解决方案

利用 pywebview 的 `js_api` 特性，通过 Python API 将图标数据传递到 JavaScript 环境中。

### 1. Python 端实现

#### 创建 API 类

```python
class LoadingAPI:
    """为 loading.html 提供的 API 接口"""
    
    def __init__(self, icon_path):
        self.icon_path = icon_path
    
    def get_icon_data(self):
        """获取图标的 base64 数据"""
        try:
            if os.path.exists(self.icon_path):
                import base64
                with open(self.icon_path, 'rb') as f:
                    icon_data = base64.b64encode(f.read()).decode('utf-8')
                return f"data:image/x-icon;base64,{icon_data}"
            else:
                print(f"图标文件不存在: {self.icon_path}")
                return None
        except Exception as e:
            print(f"读取图标文件失败: {e}")
            return None
    
    def get_icon_path(self):
        """获取图标文件路径"""
        return self.icon_path if os.path.exists(self.icon_path) else None
```

#### 传递 API 到 webview

```python
# 创建 API 对象
loading_api = LoadingAPI(ico_path)

# 创建窗口时传递 API
window = webview.create_window(
    "信小财",
    url=loading_html_path,
    width=1600,
    height=900,
    confirm_close=True,
    js_api=loading_api  # 关键：传递 API 对象到 JavaScript
)
```

### 2. JavaScript 端实现

#### 检测 API 可用性

```javascript
function loadRpaIcon() {
    // 首先检查 pywebview API 是否可用
    if (window.pywebview && window.pywebview.api) {
        console.log('使用 pywebview API 加载图标...');
        
        // 调用 Python API 获取图标数据
        window.pywebview.api.get_icon_data().then(function(iconData) {
            if (iconData) {
                console.log('通过 pywebview API 成功获取图标数据');
                createIconImage(iconData);
            } else {
                console.log('pywebview API 返回空数据，尝试 HTTP 路径...');
                tryHttpPaths();
            }
        }).catch(function(error) {
            console.log('pywebview API 调用失败:', error);
            tryHttpPaths();
        });
    } else {
        console.log('pywebview API 不可用，尝试 HTTP 路径...');
        tryHttpPaths();
    }
}
```

#### 创建图标元素

```javascript
function createIconImage(src) {
    const logoIcon = document.getElementById('logoIcon');
    const logoInner = document.getElementById('logoInner');
    
    logoIcon.style.display = 'none';
    
    const iconImg = document.createElement('img');
    iconImg.src = src;  // 可以是 base64 数据或 HTTP 路径
    iconImg.className = 'logo-image';
    iconImg.alt = '财务机器人';
    iconImg.style.objectFit = 'contain';
    iconImg.style.maxWidth = '120px';
    iconImg.style.maxHeight = '120px';
    iconImg.style.borderRadius = '10px';
    
    logoInner.insertBefore(iconImg, logoIcon);
}
```

### 3. 降级策略

实现多层降级机制，确保在各种环境下都能正常工作：

1. **优先级 1**：pywebview API（base64 数据）
2. **优先级 2**：HTTP 路径（`/cache/rpa.ico`）
3. **优先级 3**：备用路径（`/static/favicon.ico` 等）
4. **优先级 4**：保持 emoji 图标

```javascript
function tryHttpPaths() {
    const testImg = new Image();
    testImg.onload = function() {
        console.log('通过 HTTP 路径成功加载图标');
        createIconImage('/cache/rpa.ico');
    };
    testImg.onerror = function() {
        console.log('HTTP 路径加载失败，尝试其他路径...');
        tryAlternativePaths();
    };
    testImg.src = '/cache/rpa.ico';
}
```

## 优势

### 1. 可靠性
- 不依赖 HTTP 服务器状态
- 直接从 Python 端读取文件
- 支持多层降级策略

### 2. 性能
- base64 数据直接嵌入，无需额外网络请求
- 减少文件访问权限问题
- 启动时即可获取图标数据

### 3. 兼容性
- 在 pywebview 环境中工作完美
- 在浏览器环境中自动降级到 HTTP 路径
- 支持不同的图标格式

## 测试方法

### 1. 运行完整测试

```bash
python test_pywebview_icon.py
```

选择测试页面：
- 选项 1：完整的 loading.html 页面
- 选项 2：专门的图标测试页面

### 2. 仅测试 API 功能

```bash
python test_pywebview_icon.py --api-only
```

### 3. 在浏览器中测试降级

直接在浏览器中打开 `test_icon_loading.html` 查看降级效果。

## 文件修改清单

1. **`src/main_webview.py`**
   - 添加 `LoadingAPI` 类
   - 修改窗口创建代码，传递 `js_api` 参数

2. **`src/web/loading.html`**
   - 修改 `loadRpaIcon()` 函数，优先使用 pywebview API
   - 添加 `createIconImage()` 通用函数
   - 改进错误处理和降级逻辑

3. **测试文件**
   - `test_pywebview_icon.py` - 完整测试脚本
   - `test_icon_loading.html` - 专门的图标测试页面

## 注意事项

1. **API 调用是异步的**：使用 Promise 处理返回值
2. **错误处理**：确保在 API 不可用时有降级方案
3. **调试模式**：使用 `webview.start(debug=True)` 查看控制台输出
4. **图标格式**：支持 ICO、PNG、JPG 等格式

## 总结

通过使用 pywebview 的 `js_api` 特性，我们成功解决了图标传递问题：

- ✅ 图标可以在 pywebview 环境中正确显示
- ✅ 支持多种降级策略，确保兼容性
- ✅ 性能优化，减少网络请求
- ✅ 代码结构清晰，易于维护

这个解决方案充分利用了 pywebview 的特性，是在桌面应用中传递本地文件的最佳实践。
