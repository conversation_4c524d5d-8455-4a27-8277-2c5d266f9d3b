#先通过注册表获取sap的配置文件
#路径: HKEY_CURRENT_USER\SOFTWARE\SAP\SAPLogon\LandscapeFilesInUse_4588
#名称: CoreLandscapeFileOnServer
#新值: C:\Users\<USER>\AppData\Roaming\SAP\Common\SAPUILandscapeGlobal.xml (REG_SZ)
#通过配置文件获取sap账套名

import winreg
import xml.etree.ElementTree as ET
from xml.dom import minidom

def get_sap_landscape_file():
    # 定义基础路径
    base_path = r"SOFTWARE\SAP\SAPLogon"
    
    try:
        # 打开基础路径的注册表项
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, base_path) as base_key:
            index = 0
            # 遍历所有子项
            while True:
                try:
                    # 枚举子项
                    subkey_name = winreg.EnumKey(base_key, index)
                    index += 1
                    
                    # 检查子项是否以"LandscapeFilesInUse"开头
                    if subkey_name.startswith("LandscapeFilesInUse"):
                        # 构建完整路径
                        full_path = f"{base_path}\\{subkey_name}"
                        
                        try:
                            # 打开子项
                            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, full_path) as sub_key:
                                try:
                                    # 尝试获取LandscapeFile的值
                                    value, reg_type = winreg.QueryValueEx(sub_key, "LandscapeFile")
                                    # 返回值（已自动排除REG_SZ类型信息）
                                    return value
                                except FileNotFoundError:
                                    # 如果该子项中没有LandscapeFile值，继续查找下一个
                                    continue
                        except Exception as e:
                            print(f"打开子项 {full_path} 时出错: {e}")
                except OSError:
                    # 枚举完毕，没有更多子项
                    break
        
        # 如果没有找到匹配的项
        print("未找到符合条件的LandscapeFile值")
        return None
        
    except Exception as e:
        print(f"操作注册表时出错: {e}")
        return None
    

def add_sap_configs(xml_content):
    # 解析XML
    root = ET.fromstring(xml_content)
    
    # 添加Workspaces配置
    workspaces = root.find('Workspaces')
    if workspaces is None:
        # 如果Workspaces节点不存在不存在则创建
        workspaces = ET.SubElement(root, 'Workspaces')
        print("已创建Workspaces节点")
    
    # 检查指定的Workspace是否已存在
    target_workspace_uuid = "af8e7389-7875-4dec-9a0c-9498a55c92a6"
    existing_workspace = workspaces.find(f".//Workspace[@uuid='{target_workspace_uuid}']")
    
    if existing_workspace is None:
        # 创建Workspace节点
        workspace = ET.SubElement(workspaces, 'Workspace')
        workspace.set('uuid', target_workspace_uuid)
        workspace.set('name', 'Local')
        
        # 添加第一个Item
        item1 = ET.SubElement(workspace, 'Item')
        item1.set('uuid', 'f26c1d72-5e83-4785-90ef-4cc1e3263472')
        item1.set('serviceid', '324d1100-515f-40ae-a29f-743ef4bb1888')
        
        # 添加第二个Item
        item2 = ET.SubElement(workspace, 'Item')
        item2.set('uuid', '8ba1e5f5-01c2-47dd-ad40-26ff426710ad')
        item2.set('serviceid', '5542c24a-1638-42ab-a5de-0da3c2ee3edd')
        
        print("已添加Workspaces配置")
    else:
        print("Workspaces配置已存在")
    
    # 添加Messageserver配置（保留之前的功能）
    messageservers = root.find('Messageservers')
    if messageservers is None:
        messageservers = ET.SubElement(root, 'Messageservers')
    
    existing_ms = messageservers.find(".//Messageserver[@uuid='70ec32c1-74be-493d-936a-c5edb653c4fa']")
    if existing_ms is None:
        ms = ET.SubElement(messageservers, 'Messageserver')
        ms.set('uuid', '70ec32c1-74be-493d-936a-c5edb653c4fa')
        ms.set('name', 'S4P')
        ms.set('host', '*************')
        ms.set('port', '3600')
        print("已添加Messageserver配置")
    else:
        print("Messageserver配置已存在")
    
    # 添加Service配置（保留之前的功能）
    services = root.find('Services')
    if services is None:
        services = ET.SubElement(root, 'Services')
    
    existing_service = services.find(".//Service[@uuid='324d1100-515f-40ae-a29f-743ef4bb1888']")
    if existing_service is None:
        service = ET.SubElement(services, 'Service')
        service.set('type', 'SAPGUI')
        service.set('uuid', '324d1100-515f-40ae-a29f-743ef4bb1888')
        service.set('name', '生产机信小财自动生成')
        service.set('systemid', 'S4P')
        service.set('msid', '70ec32c1-74be-493d-936a-c5edb653c4fa')
        service.set('server', 'SPACE')
        service.set('sncop', '-1')
        service.set('dcpg', '2')
        print("已添加Service配置")
    else:
        print("Service配置已存在")
    
    # 格式化XML输出
    rough_string = ET.tostring(root, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")

def get_sap_name():
    landscape_file = get_sap_landscape_file()
    if landscape_file:
        #读取landscape_file文件内容
        with open(landscape_file, 'r', encoding='utf-8') as f:
            content = f.read()
        root = ET.fromstring(content)
        # 查找所有Service元素并提取name属性
        service_names = []
        for service in root.findall('.//Services/Service'):
            name = service.get('name')
            if name:
                service_names.append(name)
        return service_names
    else:
        return []
    
def wirte_sap_config():
    landscape_file = get_sap_landscape_file()
    if landscape_file:
        try:
            with open(landscape_file, 'r', encoding='utf-8') as f:
                    content = f.read()
            updated_xml = add_sap_configs(content)
            # 输出结果或写入文件
            print("\n更新后的XML内容：")
            #print(updated_xml)
            # 写入文件
            with open(landscape_file, 'w', encoding='utf-8') as f:
                f.write(updated_xml)
            print("\n已将更新后的配置写入updated_landscape.xml文件")
        except:
            pass