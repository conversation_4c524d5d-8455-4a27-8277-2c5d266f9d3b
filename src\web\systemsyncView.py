from fastapi import HTTPException
from src.web.http import app  
from fastapi import Request
import logging
import traceback

@app.post("/api/moneyIO-flow")
async def get_capital_flow_data(request: Request):
    try:
        data = await request.json()
        from src.utils.dopReport.moneyIOLedger import getTamplate
        return getTamplate(data["start_date"],data["end_date"])
    except Exception as e:
        logging.error(f"Error in get_capital_flow_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/moneyIO-flow2")
async def get_capital_flow_data2(request: Request):
    try:
        from src.utils.dopReport.moneyIOLedger import getTamplate2
        return getTamplate2()
    except Exception as e:
        logging.error(f"Error in get_capital_flow_data2: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/moneyIO-flow/update")
async def update_capital_flow_data(request: Request):
    try:
        data = await request.json()
        from src.utils.dopReport.moneyIOLedger import TamplateUpdate
        return TamplateUpdate(data)
    except Exception as e:
        logging.error(f"Error in update_capital_flow_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/fip-balance-export")
async def fip_balance_export(request: Request):
    try:
        import asyncio
        from concurrent.futures import ThreadPoolExecutor
        # 创建线程池
        executor = ThreadPoolExecutor(max_workers=1)
        data = await request.json()
        from src.utils.fundCapital.自动查询资金余额 import main
        result = await asyncio.get_event_loop().run_in_executor(executor,main, data["start_date"],data["end_date"])
        return result
    except Exception as e:
        logging.error(f"Error in fip_balance_export: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/fund-plan/initialize")
async def fund_plan_initialize(request: Request):
    try:
        from src.utils.dopReport.stupidPlan import initialize
        return initialize()
    except Exception as e:
        logging.error(f"Error in fund_plan_initialize: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/fund-plan/template")
async def fund_plan_template(request: Request):
    try:
        from src.utils.dopReport.stupidPlan import getTamplate
        return getTamplate()
    except Exception as e:
        logging.error(f"Error in fund_plan_template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/fund-plan/update")
async def fund_plan_update(request: Request):
    try:
        data = await request.json()
        from src.utils.dopReport.stupidPlan import updateTamplate
        return updateTamplate(data)
    except Exception as e:
        logging.error(f"Error in fund_plan_update: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))     

@app.post("/api/financial-special-payment/template")
async def financial_special_payment_template(request: Request):
    try:
        from src.utils.dopReport.specialPayment import getTamplate
        return getTamplate()
    except Exception as e:
        logging.error(f"Error in financial_special_payment_template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/financial-special-payment/update")
async def financial_special_payment_update(request: Request):
    try:
        data = await request.json()
        from src.utils.dopReport.specialPayment import updateTamplate
        return updateTamplate(data)
    except Exception as e:
        logging.error(f"Error in financial_special_payment_update: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))