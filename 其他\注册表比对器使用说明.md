# 注册表比对器使用说明

## 功能介绍

注册表比对器是一个用于监控Windows注册表变化的工具。它可以：

1. **第一次运行**：获取注册表快照并保存为缓存文件
2. **第二次运行**：获取新的注册表快照并与缓存进行比对
3. **显示差异**：详细显示新增、删除、修改的注册表项和值

## 使用方法

### 基本使用

```bash
# 第一次运行 - 创建基线快照
python 注册表比对器.py

# 第二次运行 - 比对差异
python 注册表比对器.py
```

### 高级选项

```bash
# 指定缓存文件路径
python 注册表比对器.py --cache-file my_registry_cache.json

# 设置扫描深度（默认为2，避免扫描时间过长）
python 注册表比对器.py --depth 3

# 指定要扫描的根键
python 注册表比对器.py --roots HKEY_CURRENT_USER HKEY_LOCAL_MACHINE

# 强制重新扫描（忽略现有缓存）
python 注册表比对器.py --force-rescan

# 保存差异报告到文件
python 注册表比对器.py --save-report

# 查看帮助
python 注册表比对器.py --help
```

## 参数说明

- `--cache-file`: 缓存文件路径（默认：registry_cache.json）
- `--depth`: 扫描深度，数值越大扫描越深入但耗时越长（默认：2）
- `--roots`: 要扫描的根键，可选值：
  - HKEY_CLASSES_ROOT
  - HKEY_CURRENT_USER（默认）
  - HKEY_LOCAL_MACHINE
  - HKEY_USERS
  - HKEY_CURRENT_CONFIG
- `--force-rescan`: 强制重新扫描，忽略现有缓存
- `--save-report`: 保存差异报告到文件

## 输出说明

### 差异类型

- **新增值**: 新增的注册表值
- **删除值**: 删除的注册表值
- **修改值**: 修改的注册表值
- **新增键**: 新增的注册表键
- **删除键**: 删除的注册表键

### 输出格式

```
发现 3 个注册表差异：
================================================================================

1. 类型: 新增值
   路径: HKEY_CURRENT_USER\Software\MyApp
   名称: Version
   新值: 1.0.0 (REG_SZ)

2. 类型: 修改值
   路径: HKEY_CURRENT_USER\Software\MyApp
   名称: LastRun
   新值: 2025-08-01 (REG_SZ)
   旧值: 2025-07-31 (REG_SZ)

3. 类型: 新增键
   路径: HKEY_CURRENT_USER\Software\MyApp\Settings
   操作: (新增键)
```

## 注意事项

1. **权限要求**: 需要足够的权限读取注册表，建议以管理员身份运行
2. **扫描时间**: 扫描深度越大，耗时越长。建议从较小的深度开始
3. **存储空间**: 注册表快照文件可能较大，请确保有足够的磁盘空间
4. **安全性**: 本工具只读取注册表，不会修改任何注册表内容

## 典型使用场景

1. **软件安装监控**: 安装软件前后比对，了解软件对注册表的修改
2. **系统变化追踪**: 定期比对，监控系统配置变化
3. **恶意软件检测**: 检测可疑的注册表修改
4. **系统维护**: 了解系统更新或配置变更的影响

## 故障排除

### 常见问题

1. **权限不足**: 以管理员身份运行程序
2. **扫描时间过长**: 减少扫描深度或限制扫描的根键
3. **内存不足**: 减少扫描范围或增加系统内存

### 错误处理

程序包含完善的错误处理机制，会在遇到问题时显示详细的错误信息。
