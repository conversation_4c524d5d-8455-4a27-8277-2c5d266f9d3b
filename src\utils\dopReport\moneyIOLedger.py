
from playwright.sync_api import Page,BrowserContext
from playwright.sync_api import ChromiumBrowserContext
import time
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import duckdb
import src.base.settings as settings
import src.utils.DB.Sql as Sql
import pandas as pd
import os
from src.utils.DB.configDB import configDB
import src.utils.fileui as fileui
from src.utils.DB.midIntrSQLiteDB import excelDB
import src.utils.Excel.openpyxlExcel as openpyExcel

class moneyIOLedger():
    def __init__(self):
        self.db=duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)
    def getTamplate(self,startDate,endDate):
        queryL=Sql.revenueAndExpenditureLedger
        queryL=queryL.replace('期初日期留',startDate)
        queryL=queryL.replace('期末日期留',endDate)
        con = self.db
        df1=con.execute(queryL).df()
        if os.path.exists(settings.PATH_EXCEL+"/中台资金余额.xlsx"):
            data=openpyExcel.getUsedListByPath(settings.PATH_EXCEL+"/中台资金余额.xlsx")
            d={}
            for i in range(len(data)):
                key=data[i][6]
                if type(key)==str:
                    key=key.replace("(内行账户)","")
                    d[key]=data[i][14]
            df1["一体化余额"]=df1["项目名称"].map(d)
        directory = fileui.select_directory()
        df1.to_excel(directory+"/财商收支台账模板.xlsx",sheet_name="财商收支模板")

    def td_input_next(self,s,c):
        s="//td[contains(text(),'"+s+"')]/parent::tr/following-sibling::tr[1]/td["+str(c+1)+"]//input"
        return s

    def td_input2(self,s,c):
        s="//td[contains(text(),'"+s+"')]/following-sibling::td["+str(c)+"]//input"
        return s

    def td_div_next(self,s,c):
        s="//td[contains(text(),'"+s+"')]/parent::tr/following-sibling::tr[1]/td["+str(c+1)+"]//div"
        return s

    def td_div2(self,s,c):
        s="//td[contains(text(),'"+s+"')]/following-sibling::td["+str(c)+"]//div"
        return s
    def returnstring(self,s):
        if (type(s)==float or type(s)==int) and not pd.isna(s) :
            s=str(s)
            return s
        else:
            return "0.00"

    def label_input(self,s):
        s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]//input"
        return s

    
    def autoOne(self,context:BrowserContext,page:Page,d: dict):  
        def checkStr(s):
            if type(s)==float or type(s)==int  :
                return str(int(s))
            else:
                return s
        def checkDate(s):
            from datetime import datetime, timedelta
            if type(s)==int:
                base_date = datetime(1900, 1, 1)
                # 计算实际日期
                actual_date = base_date + timedelta(days = s - 1)
                # 格式化为'YYYY年MM月'格式
                formatted_date = actual_date.strftime('%Y年%m月')
                return formatted_date
            else:
                return s
        page.locator("//div[text()='当前项目：']/following-sibling::div[1]/div[1]").click()
        page.locator("//*[@id='portal-form-list-container']/div[3]/div/div/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[1]/div[2]/input").fill(checkStr(d["项目编号"]))
        page.locator("//*[@id='portal-form-list-container']/div[3]/div/div/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[5]/button").click()
        page.locator("//td[normalize-space()='"+checkStr(d["项目编号"])+"']").click()
        #选择项目
        
        with context.expect_page() as new_page_info:
            page.locator("//button[@class='ant-btn ant-btn-primary list-action-add']").click() 
        new_page = new_page_info.value

        new_page.locator("//label[contains(text(),'本级单位机关项目')]/parent::div/following-sibling::div[1]//span").click()
        new_page.get_by_text(d["上级机关"]).click()
        new_page.get_by_text("确 定").click()

        #选择日期
        new_page.locator("//label[contains(text(),'"+"日期"+"')]/parent::div/following-sibling::div[1]//span").first.click()
        if d["是否跨年"]=="是":
            new_page.locator("//span[@title='"+d["第几季度"][:5]+"']/preceding-sibling::span[1]").click()
        new_page.locator("//span[@title='"+d["第几季度"]+"']/preceding-sibling::span[1]").click()

        new_page.locator("//span[@title='"+checkDate(d["几月"])+"']").click()
        new_page.locator("//div[contains(text(),'期间')]/parent::div/parent::div//span[contains(text(),'确 定')]").click()
        
        new_page.get_by_text("本月资金收入").first.dblclick()
        new_page.get_by_text("其中，其他收入").first.click()
        cscec.getVisible(new_page,"//td[contains(text(),'其他')]/following-sibling::td[1]").click()
        cscec.getVisible(new_page,"//td[contains(text(),'其他')]/following-sibling::td[1]//input").fill(self.returnstring(d["其他收入"]))


        new_page.get_by_text("本月资金支出").first.dblclick()
        new_page.locator(self.label_input("增值税外地预缴支出")).first.fill(self.returnstring(d["预缴税金"]))
        new_page.get_by_text("其中，其他支出").first.click()

        new_page.get_by_text("其中，其他税金支出").first.click()

        new_page.locator(self.td_div2("其它影响成本的税金",1)).click()
        new_page.locator(self.td_input2("其它影响成本的税金",1)).fill(self.returnstring(d["其他税金"]))

        new_page.get_by_text("其中，现场管理费及规费支出").first.dblclick()

        new_page.locator(self.td_div2("包干-职工薪酬",1)).click()
        new_page.locator(self.td_input2("包干-职工薪酬",1)).fill(self.returnstring(d["薪酬"]))
        

        new_page.locator(self.td_div2("其中，办公费",1)).click()
        new_page.locator(self.td_input2("其中，办公费",1)).fill(self.returnstring(d["办公费"]))


        new_page.locator(self.td_div2("其中，差旅费",1)).click()
        new_page.locator(self.td_input2("其中，差旅费",1)).fill(self.returnstring(d["差旅费"]))


        new_page.locator(self.td_div2("其中，业务招待费",1)).click()
        new_page.locator(self.td_input2("其中，业务招待费",1)).fill(self.returnstring(d["业务招待费"]))

        new_page.locator(self.td_div2("非包-其他专项支出",1)).click()
        new_page.locator(self.td_input2("非包-其他专项支出",1)).fill(self.returnstring(d["非包-其他专项支出"]))

        new_page.get_by_text("本月资金结余").first.click()
        money=float(new_page.locator("//label[contains(text(),'本月资金结余')]/parent::div/following-sibling::div[1]//div[@role='spinbutton']").first.get_attribute("aria-valuenow"))
        money2=float(new_page.locator("//label[contains(text(),'本月资金结余（校验）')]/parent::div/following-sibling::div[1]//div[@role='spinbutton']").first.get_attribute("aria-valuenow"))
        if round(money2-money,3)!=0:
            new_page.locator(self.label_input("修正(特殊情况)")).first.fill(self.returnstring(round(money2-money,4)))
            new_page.locator("//label[contains(text(),'修正原因')]/parent::div/following-sibling::div[1]//textarea").first.fill("资金修正")

        new_page.get_by_text("本月资金支出").first.dblclick()
        new_page.get_by_text("其中，其他支出").first.click()
        new_page.locator(self.td_div_next("材料调拨",1)).click()
        new_page.locator(self.td_input_next("材料调拨",1)).fill(self.returnstring(round(money-d["一体化余额"]+money2-money,4))) #通过材料调拨定位其他,强行抹平差额


        new_page.get_by_text("保 存").click()
        time.sleep(3)
        new_page.close()

    def fillmoneyLedger(self):
        configDict=configDB().fqUserSettings
        Br=browser.myBrowser("3b",configDict["username"],configDict["password"])
        page=Br.page
        context=Br.Context

        page.locator("//span[@title='资金管理']").click()
        page.locator("//td[normalize-space()='1']").click()
        page.locator("//span[@title='资金收支管理']").click()
        page.locator("//a[@title='资金收支台账']/parent::li").click()
        page.locator("//li[text()='资金收支台账']/following-sibling::li[1]").click()    
        time.sleep(2) 
        print("开始填入")
        db=excelDB()
        df=db.getDataframe("财商收支台账")
        try:
            for index,row in df.iterrows():
                if row["是否填入"]=="是":
                    print(f"第{index+1}行开始填入")
                    db.updateData("财商收支台账",index+1,"是否填入","开始执行")
                    d=row.to_dict()
                    self.autoOne(context,page,d)
                    db.updateData("财商收支台账",index+1,"是否填入","执行完毕")
        except Exception as e:
            print(e)
            print(f"第{index+1}行出错")
            db.close()
        db.close()
        print("完成一次")


def createTemplate(startDate,endDate):
    moneyIOLedger().getTamplate(startDate,endDate)

def queryData():
    db=excelDB()
    db.queryData("财商收支台账")
    db.close()

def writeData():
    db=excelDB()
    db.writeExcel("财商收支台账")
    db.close()

def getTamplate(startDate,endDate):
    db=duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)
    queryL=Sql.revenueAndExpenditureLedger
    queryL=queryL.replace('期初日期留',startDate)
    queryL=queryL.replace('期末日期留',endDate)
    con = db
    df1=con.execute(queryL).df()
    df1.fillna(0, inplace=True)
    return [df1.columns.tolist()]+df1.values.tolist()

def getTamplate2():
    conn=excelDB()
    try:
        df = pd.read_sql("SELECT * FROM 财商收支台账", conn.conn)
    except:
        df = pd.DataFrame(columns=["请先初始化一次数据推送"])
    conn.conn.close()
    return {"财商收支台账":[df.columns.tolist()]+df.values.tolist()}

def TamplateUpdate(data):
    conn=excelDB()
    data=data["currentData"]["财商收支台账"]
    processed_columns = [f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(data[0])]
    df = pd.DataFrame(columns=processed_columns,data=data[1:])
    df.to_sql("财商收支台账", conn.conn, if_exists='replace', index=False)
    conn.conn.close()
    
def main():
    moneyIOLedger().fillmoneyLedger()