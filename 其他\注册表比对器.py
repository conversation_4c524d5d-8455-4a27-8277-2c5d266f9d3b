#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注册表比对器
功能：
1. 第一次运行时获取注册表快照并保存为缓存
2. 第二次运行时获取新的注册表快照并与缓存进行比对
3. 显示差异的注册表项和值

作者：AI Assistant
日期：2025-08-01
"""

import winreg
import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional


class RegistryComparator:
    """注册表比对器类"""

    def __init__(self, cache_file: str = "registry_cache.json"):
        """
        初始化注册表比对器

        Args:
            cache_file: 缓存文件路径
        """
        self.cache_file = cache_file
        self.registry_roots = {
            'HKEY_CLASSES_ROOT': winreg.HKEY_CLASSES_ROOT,
            'HKEY_CURRENT_USER': winreg.HKEY_CURRENT_USER,
            'HKEY_LOCAL_MACHINE': winreg.HKEY_LOCAL_MACHINE,
            'HKEY_USERS': winreg.HKEY_USERS,
            'HKEY_CURRENT_CONFIG': winreg.HKEY_CURRENT_CONFIG
        }

    def get_registry_value_type_name(self, value_type: int) -> str:
        """获取注册表值类型名称"""
        type_names = {
            winreg.REG_NONE: "REG_NONE",
            winreg.REG_SZ: "REG_SZ",
            winreg.REG_EXPAND_SZ: "REG_EXPAND_SZ",
            winreg.REG_BINARY: "REG_BINARY",
            winreg.REG_DWORD: "REG_DWORD",
            winreg.REG_DWORD_BIG_ENDIAN: "REG_DWORD_BIG_ENDIAN",
            winreg.REG_LINK: "REG_LINK",
            winreg.REG_MULTI_SZ: "REG_MULTI_SZ",
            winreg.REG_RESOURCE_LIST: "REG_RESOURCE_LIST",
            winreg.REG_FULL_RESOURCE_DESCRIPTOR: "REG_FULL_RESOURCE_DESCRIPTOR",
            winreg.REG_RESOURCE_REQUIREMENTS_LIST: "REG_RESOURCE_REQUIREMENTS_LIST",
            winreg.REG_QWORD: "REG_QWORD"
        }
        return type_names.get(value_type, f"UNKNOWN_TYPE_{value_type}")

    def serialize_registry_value(self, value: Any, value_type: int) -> str:
        """序列化注册表值为字符串"""
        try:
            if value_type == winreg.REG_BINARY:
                # 二进制数据转换为十六进制字符串
                if isinstance(value, bytes):
                    return value.hex()
                else:
                    return str(value)
            elif value_type == winreg.REG_MULTI_SZ:
                # 多字符串值
                if isinstance(value, list):
                    return json.dumps(value, ensure_ascii=False)
                else:
                    return str(value)
            else:
                return str(value)
        except Exception as e:
            return f"<序列化错误: {str(e)}>"

    def read_registry_key(self, root_key, key_path: str, max_depth: int = 3, current_depth: int = 0) -> Dict[str, Any]:
        """
        递归读取注册表项

        Args:
            root_key: 根键句柄
            key_path: 键路径
            max_depth: 最大递归深度
            current_depth: 当前递归深度

        Returns:
            包含注册表数据的字典
        """
        result = {
            'values': {},
            'subkeys': {}
        }

        try:
            with winreg.OpenKey(root_key, key_path, 0, winreg.KEY_READ) as key:
                # 读取值
                try:
                    i = 0
                    while True:
                        try:
                            value_name, value_data, value_type = winreg.EnumValue(key, i)
                            result['values'][value_name or '(默认)'] = {
                                'data': self.serialize_registry_value(value_data, value_type),
                                'type': self.get_registry_value_type_name(value_type)
                            }
                            i += 1
                        except OSError:
                            break
                except Exception as e:
                    pass

                # 读取子键（如果未达到最大深度）
                if current_depth < max_depth:
                    try:
                        i = 0
                        while True:
                            try:
                                subkey_name = winreg.EnumKey(key, i)
                                subkey_path = f"{key_path}\\{subkey_name}" if key_path else subkey_name
                                result['subkeys'][subkey_name] = self.read_registry_key(
                                    root_key, subkey_path, max_depth, current_depth + 1
                                )
                                i += 1
                            except OSError:
                                break
                    except Exception as e:
                        pass

        except Exception as e:
            result['error'] = str(e)

        return result

    def get_registry_snapshot(self, roots_to_scan: List[str] = None, max_depth: int = 3) -> Dict[str, Any]:
        """
        获取注册表快照

        Args:
            roots_to_scan: 要扫描的根键列表，默认扫描常用的根键
            max_depth: 扫描深度

        Returns:
            注册表快照数据
        """
        if roots_to_scan is None:
            # 默认只扫描用户相关的根键，避免扫描时间过长
            roots_to_scan = ['HKEY_CURRENT_USER', 'HKEY_LOCAL_MACHINE']

        snapshot = {
            'timestamp': datetime.now().isoformat(),
            'roots': {}
        }

        for root_name in roots_to_scan:
            if root_name in self.registry_roots:
                print(f"正在扫描 {root_name}...")
                try:
                    snapshot['roots'][root_name] = self.read_registry_key(
                        self.registry_roots[root_name], "", max_depth
                    )
                except Exception as e:
                    print(f"扫描 {root_name} 时出错: {e}")
                    snapshot['roots'][root_name] = {'error': str(e)}

        return snapshot

    def save_snapshot(self, snapshot: Dict[str, Any]) -> None:
        """保存快照到文件"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(snapshot, f, ensure_ascii=False, indent=2)
            print(f"快照已保存到: {self.cache_file}")
        except Exception as e:
            print(f"保存快照失败: {e}")

    def load_snapshot(self) -> Optional[Dict[str, Any]]:
        """从文件加载快照"""
        try:
            if not os.path.exists(self.cache_file):
                return None
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载快照失败: {e}")
            return None

    def compare_values(self, old_values: Dict[str, Any], new_values: Dict[str, Any], path: str) -> List[Dict[str, Any]]:
        """比较注册表值"""
        differences = []

        # 检查新增的值
        for name, new_value in new_values.items():
            if name not in old_values:
                differences.append({
                    'type': 'added',
                    'path': path,
                    'name': name,
                    'new_value': new_value,
                    'old_value': None
                })
            elif old_values[name] != new_value:
                differences.append({
                    'type': 'modified',
                    'path': path,
                    'name': name,
                    'new_value': new_value,
                    'old_value': old_values[name]
                })

        # 检查删除的值
        for name, old_value in old_values.items():
            if name not in new_values:
                differences.append({
                    'type': 'deleted',
                    'path': path,
                    'name': name,
                    'new_value': None,
                    'old_value': old_value
                })

        return differences

    def compare_registry_keys(self, old_data: Dict[str, Any], new_data: Dict[str, Any], path: str = "") -> List[Dict[str, Any]]:
        """递归比较注册表键"""
        differences = []

        # 比较值
        old_values = old_data.get('values', {})
        new_values = new_data.get('values', {})
        differences.extend(self.compare_values(old_values, new_values, path))

        # 比较子键
        old_subkeys = old_data.get('subkeys', {})
        new_subkeys = new_data.get('subkeys', {})

        # 检查新增和修改的子键
        for subkey_name, new_subkey_data in new_subkeys.items():
            subkey_path = f"{path}\\{subkey_name}" if path else subkey_name
            if subkey_name not in old_subkeys:
                # 新增的子键
                differences.append({
                    'type': 'key_added',
                    'path': subkey_path,
                    'name': '',
                    'new_value': '(新增键)',
                    'old_value': None
                })
                # 递归添加所有子项作为新增
                differences.extend(self._get_all_items_as_added(new_subkey_data, subkey_path))
            else:
                # 递归比较子键
                differences.extend(self.compare_registry_keys(old_subkeys[subkey_name], new_subkey_data, subkey_path))

        # 检查删除的子键
        for subkey_name in old_subkeys:
            if subkey_name not in new_subkeys:
                subkey_path = f"{path}\\{subkey_name}" if path else subkey_name
                differences.append({
                    'type': 'key_deleted',
                    'path': subkey_path,
                    'name': '',
                    'new_value': None,
                    'old_value': '(删除键)'
                })

        return differences

    def _get_all_items_as_added(self, data: Dict[str, Any], path: str) -> List[Dict[str, Any]]:
        """获取所有项目作为新增项"""
        differences = []

        # 添加所有值
        for name, value in data.get('values', {}).items():
            differences.append({
                'type': 'added',
                'path': path,
                'name': name,
                'new_value': value,
                'old_value': None
            })

        # 递归添加所有子键
        for subkey_name, subkey_data in data.get('subkeys', {}).items():
            subkey_path = f"{path}\\{subkey_name}"
            differences.append({
                'type': 'key_added',
                'path': subkey_path,
                'name': '',
                'new_value': '(新增键)',
                'old_value': None
            })
            differences.extend(self._get_all_items_as_added(subkey_data, subkey_path))

        return differences

    def compare_snapshots(self, old_snapshot: Dict[str, Any], new_snapshot: Dict[str, Any]) -> List[Dict[str, Any]]:
        """比较两个快照"""
        all_differences = []

        for root_name in new_snapshot.get('roots', {}):
            if root_name in old_snapshot.get('roots', {}):
                print(f"正在比较 {root_name}...")
                differences = self.compare_registry_keys(
                    old_snapshot['roots'][root_name],
                    new_snapshot['roots'][root_name],
                    root_name
                )
                all_differences.extend(differences)
            else:
                print(f"{root_name} 在旧快照中不存在，跳过比较")

        return all_differences

    def format_differences(self, differences: List[Dict[str, Any]]) -> str:
        """格式化差异输出"""
        if not differences:
            return "没有发现注册表差异。"

        output = []
        output.append(f"发现 {len(differences)} 个注册表差异：")
        output.append("=" * 80)

        for i, diff in enumerate(differences, 1):
            output.append(f"\n{i}. 类型: {self._get_diff_type_name(diff['type'])}")
            output.append(f"   路径: {diff['path']}")
            if diff['name']:
                output.append(f"   名称: {diff['name']}")

            if diff['type'] in ['added', 'modified']:
                if diff['new_value']:
                    output.append(f"   新值: {diff['new_value']['data']} ({diff['new_value']['type']})")

            if diff['type'] in ['deleted', 'modified']:
                if diff['old_value']:
                    output.append(f"   旧值: {diff['old_value']['data']} ({diff['old_value']['type']})")

            if diff['type'] in ['key_added', 'key_deleted']:
                output.append(f"   操作: {diff['new_value'] or diff['old_value']}")

        return "\n".join(output)

    def _get_diff_type_name(self, diff_type: str) -> str:
        """获取差异类型的中文名称"""
        type_names = {
            'added': '新增值',
            'deleted': '删除值',
            'modified': '修改值',
            'key_added': '新增键',
            'key_deleted': '删除键'
        }
        return type_names.get(diff_type, diff_type)

    def save_differences_to_file(self, differences: List[Dict[str, Any]], filename: str = None) -> None:
        """保存差异到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"registry_differences_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"注册表比对结果\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 80 + "\n\n")
                f.write(self.format_differences(differences))
            print(f"差异报告已保存到: {filename}")
        except Exception as e:
            print(f"保存差异报告失败: {e}")


def main():
    """主程序"""
    print("注册表比对器")
    print("=" * 50)

    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='注册表比对器')
    parser.add_argument('--cache-file', default='registry_cache.json', help='缓存文件路径')
    parser.add_argument('--depth', type=int, default=5, help='扫描深度 (默认: 2)')
    parser.add_argument('--roots', nargs='+',
                       choices=['HKEY_CLASSES_ROOT', 'HKEY_CURRENT_USER', 'HKEY_LOCAL_MACHINE', 'HKEY_USERS', 'HKEY_CURRENT_CONFIG'],
                       default=['HKEY_CURRENT_USER'],
                       help='要扫描的根键 (默认: HKEY_CURRENT_USER)')
    parser.add_argument('--force-rescan', action='store_true', help='强制重新扫描，忽略现有缓存')
    parser.add_argument('--save-report', action='store_true', help='保存差异报告到文件')

    args = parser.parse_args()

    comparator = RegistryComparator(args.cache_file)

    # 检查是否存在缓存文件
    cached_snapshot = None if args.force_rescan else comparator.load_snapshot()

    if cached_snapshot is None:
        print("未找到缓存文件或强制重新扫描，正在创建注册表快照...")
        print(f"扫描根键: {', '.join(args.roots)}")
        print(f"扫描深度: {args.depth}")
        print("注意：扫描可能需要较长时间，请耐心等待...")

        snapshot = comparator.get_registry_snapshot(args.roots, args.depth)
        comparator.save_snapshot(snapshot)
        print("首次扫描完成！请再次运行程序进行比对。")
    else:
        print("找到缓存文件，正在进行新的扫描并比对...")
        print(f"缓存时间: {cached_snapshot.get('timestamp', '未知')}")
        print(f"扫描根键: {', '.join(args.roots)}")
        print(f"扫描深度: {args.depth}")

        new_snapshot = comparator.get_registry_snapshot(args.roots, args.depth)

        print("正在比对注册表差异...")
        differences = comparator.compare_snapshots(cached_snapshot, new_snapshot)

        # 显示结果
        print("\n" + "=" * 80)
        print(comparator.format_differences(differences))

        # 保存新快照
        comparator.save_snapshot(new_snapshot)

        # 保存差异报告
        if args.save_report and differences:
            comparator.save_differences_to_file(differences)

        print(f"\n比对完成！发现 {len(differences)} 个差异。")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断。")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()