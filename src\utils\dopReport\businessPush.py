
from playwright.sync_api import Playwright, sync_playwright,Page
from playwright.sync_api import ChromiumBrowserContext
import src.utils.dop as dop
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import src.utils.Excel.openpyxlExcel as openpyxlExcel
import src.base.settings as settings
import src.utils.Excel.excel as excel
from datetime import datetime
import time
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd

def fillbyname(page:Page, name:str,inputText:str):
    s=f"//span[contains(text(),'{name}')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1=f"//span[contains(text(),'{name}')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(inputText)
    s1=f"//span[contains(text(),'{name}')]/parent::div/parent::div/parent::td"
    page.locator(s1).click()

def simpleFill(page:Page,d:dict): 
    page.locator("//div[contains(text(),'分包结算')]").click()
    page.get_by_text("过程应付款申请",exact=True).nth(0).click()
    project_code=d["项目编号"] if isinstance(d["项目编号"],str) else str(int(d["项目编号"]))
    page.locator("//input[@placeholder='请输入标准项目编码']").fill(project_code)
    page.locator("//span[contains(text(),'查 询')]").click()
    page.locator(f"//td[text()='1']/following-sibling::td[1]/div/a").click()
    page.get_by_text("分供合同名称").click()
    page.locator("//span[contains(text(),'新增')]").click()
    page.locator("//li[contains(text(),'应付款申请(简易)')]").click()
    s="//div[text()='分包单位名称']/parent::span/parent::label/parent::div/parent::div//input[@placeholder='请输入']"
    page.locator(s).fill(d["客商名称"])
    page.locator("//span[contains(text(),'搜 索')]").click()
    page.locator(s).click()#此处能点击说明搜索完成
    contract_code=d["商务系统合同id"] if isinstance(d["商务系统合同id"],str) else str(int(d["商务系统合同id"]))
    try:#尝试
        page.locator(f"//tr[@data-row-key='{contract_code}']").click(timeout=100) #改为合同重新查找
        page.locator("//span[contains(text(),'确认')]").click()
    except:
        return "合同映射有问题或者有在途单据"

    s="//div[text()='所属月份']/parent::span/parent::label/parent::div/parent::div//input[@placeholder='请选择']"
    page.locator(s).click()
    page.locator("//td//div[text()='"+d["月份"]+"']").click()
    s="//div[contains(text(),'是否最终结算款申请')]/parent::span/parent::label/parent::div/parent::div//input"
    page.locator(s).click()
    page.locator("//div[@class='g3-scm-select-item-option-content'][contains(text(),'否')]").click()

    page.locator("//textarea[@placeholder='请输入']").fill(d["合同业务内容"])




    fillbyname(page,"合约内工程造价（不含税）",str(round(d["不含税结算额"],2)))

    
    s="//span[contains(text(),'其中：安全文明施工费（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'其中：安全文明施工费（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")

    s="//span[contains(text(),'应付工程款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'应付工程款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    if d["不含税结算额"]>1:
        page.locator(s).fill(str(round(d["不含税结算额"]*d["付款比例"],2)))
        s="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
        s1="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s1="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[1]"
        page.locator(s1).click()
    else:
        s2="//span[contains(text(),'应付工程款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[3]/div/div"
        if float(page.locator(s2).text_content())>1:
            fillAmout="-"+str(round(float(page.locator(s2).text_content())-1,2)) #调整一元钱
            page.locator(s).fill(fillAmout)
        else:
            page.locator(s).fill("0")
        page.locator(s2).click()
        s="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
        s1="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
        s2="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[3]/div/div"
        page.locator(s1).click()
        if s2!="0":
            fillAmout="-"+page.locator(s2).text_content()
            page.locator(s).fill(fillAmout)
        s1="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[1]"
        page.locator(s1).click()
        #负数非常离谱，要小心
    
    x=page.locator(s1).bounding_box()["height"]
    page.mouse.wheel(0,12*x)
    s="//span[contains(text(),'质保金税金')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'质保金税金')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(d["质保金税金"],2)))

    s="//span[contains(text(),'质保金税金')]/parent::div/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//span[contains(text(),'质保金税金')]/parent::div/parent::div/parent::td/following-sibling::td[2]" #税率
    page.locator(s1).click()
    page.locator(s).fill(str(int(d["税率整数"])))
    page.locator("//span[contains(text(),'质保金税金')]/parent::div/parent::div/parent::td/following-sibling::td[1]").click()

    page.mouse.wheel(0,-6*x)# 滚动鼠标 , 参数给一个较大值，以保证直接移动至最上
    s="//span[contains(text(),'奖励小计')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'奖励小计')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    s1="//span[contains(text(),'奖励小计')]"
    page.locator(s1).click()
    
    page.mouse.wheel(0,-6*x)# 滚动鼠标 , 参数给一个较大值，以保证直接移动至最上
    s="//span[contains(text(),'预付款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'预付款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")

    s="//span[contains(text(),'其中：人工费（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'其中：人工费（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")

    s="//span[contains(text(),'其中：安全文明施工费（不含税')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'其中：安全文明施工费（不含税')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")

    s="//span[contains(text(),'变更签证工程造价（不含税')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'变更签证工程造价（不含税')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    page.locator("//span[contains(text(),'变更签证工程造价（不含税')]/parent::div/parent::div/parent::td/following-sibling::td[1]").click()


    

    page.mouse.wheel(0,12*x)# 滚动鼠标 , 参数给一个较大值，以保证直接移动至最后

    s="//span[contains(text(),'税金小计')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'税金小计')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(d["税额"],2)))

    s="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(d["付款比例"]*100,2)))

    s="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(d["按合同应付差异"],2)))
    s1="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td"
    page.locator(s1).click()
    page.locator(s1).click()
    page.locator(s1).click()

    
    page.get_by_role("tab", name="按合同其他应付款项明细").click()
    s="//div[contains(text(),'预扣税')]/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//div[contains(text(),'预扣税')]/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    s="//div[contains(text(),'农民工工资保证金')]/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//div[contains(text(),'农民工工资保证金')]/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    s="//div[contains(text(),'履约保证金')]/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//div[contains(text(),'履约保证金')]/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    s="//div[contains(text(),'安全保证金')]/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//div[contains(text(),'安全保证金')]/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    s="//div[contains(text(),'其他保证金')]/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//div[contains(text(),'其他保证金')]/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill("0")


    if d["合同类型"].find("分包")!=-1:
        page.get_by_role("tab", name="税前扣款").click()
        s="//div[contains(text(),'总包管理配合费')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'总包管理配合费')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'现场管理费')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'现场管理费')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'维修费用')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'维修费用')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'临建费用')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'临建费用')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'规费')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'规费')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'罚款')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'罚款')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'甲供材及超供扣款')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'甲供材及超供扣款')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'水费')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'水费')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'电费')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'电费')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'电费')]/parent::div/parent::td/parent::tr/following-sibling::tr[1]/td[4]//input"
        s1="//div[contains(text(),'电费')]/parent::div/parent::td/parent::tr/following-sibling::tr[1]/td[4]"
        page.locator(s1).click()
        page.locator(s).fill("0")
    with page.expect_file_chooser() as fc_info:
        page.locator("//span[contains(text(),'点击或将文件拖拽至框内上传')]").click() 
        file_chooser = fc_info.value
        file_chooser.set_files(d["附件地址"])
    page.locator("//span[contains(text(),'保存')]").click()
    page.locator("//span[contains(text(),'确认')]").click()
    page.locator("//button[@class='g3-scm-btn g3-scm-btn-primary g3-button']").click()
    page.locator("//span[contains(text(),'确认')]").click()
    cscec.getVisible(page,"//td[text()='新-应付款推送']/preceding::td[2]").click()
    cscec.getVisible(page,"//button[@class='ant-btn ant-btn-primary']").click()




     
def queryData(ifFile=False):
    db=excelDB()
    headers = ["合同类型","项目编号","客商名称","商务系统合同id","月份","不含税结算额","付款比例","结算差异","按合同应付差异","质保金税金","税率整数","附件地址","税额","是否写入"]
    datarows=["123456789","中建三局","123456789","3月","1000","0.8","0","0","0","0","0","0","0","是"]
    if ifFile:
        try:
            df = pd.read_sql("SELECT * FROM 商务系统批量推送", db.conn)
            df.fillna("", inplace=True) 
        except: 
            df=pd.DataFrame(columns=headers,data=[datarows])
            return {"商务系统批量推送":[df.columns.tolist()]+df.values.tolist()}
        df=pd.DataFrame(columns=headers,data=[datarows])
        return {"商务系统批量推送":[df.columns.tolist()]+df.values.tolist()}
    db.queryData("商务系统批量推送",headers,datarows)
    db.close()

def writeData(data=None):
    db=excelDB()
    if data is None:
        db.writeExcel("商务系统批量推送")
    else:
        title=data["商务系统批量推送"][0]
        processTitle=[f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(title)]
        df=pd.DataFrame(columns=processTitle,data=data["商务系统批量推送"][1:])
        df.to_sql("商务系统批量推送", db.conn, if_exists='replace', index=False)
    db.close()

def main():
    """
    批量提交商务系统推送
    """
    db=excelDB()
    df=db.getDataframe("商务系统批量推送")

    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"商务管理系统")
        print(f"共有{df.index.size}条数据")
        for i,row in df.iterrows():
            print(f"正在处理第{i+1}条数据")
            d=row.to_dict()
            if d["是否写入"]=="是":
                db.updateData("商务系统批量推送",i+1,"是否写入","处理中")
                Msg=simpleFill(page,d)
                if Msg is not None:
                    db.updateData("商务系统批量推送",i+1,"是否写入",Msg)
                db.updateData("商务系统批量推送",i+1,"是否写入","已完成")



