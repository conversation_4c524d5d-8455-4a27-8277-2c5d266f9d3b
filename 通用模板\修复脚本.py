import os
import requests
import sys
import threading
import tkinter as tk
from tkinter import ttk, messagebox

def download_files(progress_var, status_var, button):
    try:
        path = os.path.dirname(sys.executable)
        files = [
            ('https://cscec3b-fip.hb11oss.ctyunxs.cn/ftools.zip', os.path.join(path, 'ftools2.zip')),
            ('https://cscec3b-fip.hb11oss.ctyunxs.cn/main2.pyc', os.path.join(path, 'main2.pyc'))
        ]
        total = len(files)
        for i, (url, save_path) in enumerate(files, 1):
            status_var.set(f"正在下载: {os.path.basename(save_path)}")
            response = requests.get(url)
            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    f.write(response.content)
            else:
                status_var.set(f"下载失败: {os.path.basename(save_path)}")
                messagebox.showerror("错误", f"{os.path.basename(save_path)} 下载失败！")
                button.config(state=tk.NORMAL)
                return
            progress_var.set(int(i / total * 100))
        status_var.set("下载完成！")
        messagebox.showinfo("提示", "所有文件下载完成！")
    except Exception as e:
        status_var.set("下载出错！")
        messagebox.showerror("错误", str(e))
    finally:
        button.config(state=tk.NORMAL)

def start_download(progress_var, status_var, button):
    button.config(state=tk.DISABLED)
    threading.Thread(target=download_files, args=(progress_var, status_var, button), daemon=True).start()

def main():
    root = tk.Tk()
    root.title("文件下载器")
    root.geometry("350x150")
    progress_var = tk.IntVar()
    status_var = tk.StringVar(value="等待下载...")
    ttk.Label(root, textvariable=status_var).pack(pady=10)
    progressbar = ttk.Progressbar(root, length=250, variable=progress_var, maximum=100)
    progressbar.pack(pady=10)
    download_btn = ttk.Button(root, text="开始下载", command=lambda: start_download(progress_var, status_var, download_btn))
    download_btn.pack(pady=10)

    root.mainloop()

if __name__ == "__main__":
    main()

