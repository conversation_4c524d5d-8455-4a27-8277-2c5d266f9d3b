
import tkinter as tk
from tkinter import filedialog
import tkinter.ttk as ttk
import src.base.settings as settings
import ctypes


def select_directory(title=None):
    # Windows系统：启用高DPI感知
    try:
        ctypes.windll.shcore.SetProcessDpiAwareness(1)  # 对于Windows 8.1+
    except:
        pass  # 旧系统忽略
    if title is not None:
        title="请选择一个目录"
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 设置窗口置顶
    root.attributes('-topmost', True)
    root.iconbitmap(settings.CACHE_PATH+"/rpa.ico")  # 设置窗口图标
    # 打开目录选择对话框
    selected_directory = filedialog.askdirectory(
        title=title,
        initialdir="/"  # 默认初始目录（可根据需要修改）
    )

    # 销毁隐藏的根窗口
    root.destroy()
    return selected_directory

def select_file(initialdir="/",title="请选择一个文件"):
    # Windows系统：启用高DPI感知
    try:
        ctypes.windll.shcore.SetProcessDpiAwareness(1)  # 对于Windows 8.1+
    except:
        pass  # 旧系统忽略
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 设置窗口置顶
    root.attributes('-topmost', True)
    root.iconbitmap(settings.CACHE_PATH+"/rpa.ico")  # 设置

    select_path = filedialog.askopenfilename(
        title=title,
        initialdir=initialdir  # 默认初始目录（可根据需要修改）
    )
    # 销毁隐藏的根窗口
    root.destroy()
    return select_path

