from flet.core.page import Page
import src.utils.cscec as cscec
from playwright.sync_api import Playwright, sync_playwright
import time
import src.utils.Excel.excel as excel
import src.utils.Browser.Browser as browser
from src.utils.DB.midIntrSQLiteDB import excelDB
import src.utils.fileui as fileui
import pandas as pd

class salary():
    def __init__(self):
        pass
    def confirmSalary(self):
        conn=excelDB()
        df= pd.read_sql("SELECT * FROM 劳务派遣认领", conn.conn)
        page = browser.myBrowser("cscec").page
        print(f"共有{df.index.size}条数据")
        for i,row in df.iterrows():
                if row["项目名称"].find("机关")==-1 and row["是否分割"]=="是":
                    conn.updateData("劳务派遣认领","是否分割","本行执行中",i+1)
                    print("开始第"+str(i+1)+"行")
                    cscec.changeProjectCscec(page,row["单位"],row["项目名称"])
                    page.locator("//span[text()='报账系统']/parent::span").click()
                    page.locator("//span[text()='员工薪酬及费用']/parent::li").click()
                    page.locator("//li/span[text()='同法人费用分割认领单']").click()
                    tr=cscec.getVisible(page,cscec.getTr(page,"费用分割机构"))
                    cscec.getVisible(page,"//div[text()='确认承担']").click()
                    
                    page.locator("//label[contains(text(),'事　　由')]/parent::div/following-sibling::div[1]//input").click()
                    page.locator("//label[contains(text(),'事　　由')]/parent::div/following-sibling::div[1]//input").fill(row["事由"])
                    page.get_by_text("组织机构：").click()
                    
                    pageTable=cscec.cscecTable(page,"承担部门")
                    pageTable.reIndex()
                    pageTable.clickInputQuery2(1,"*成本费用属性")
                    cscec.getVisible(page,"//input[@placeholder='请输入查询关键字']").fill("0210")
                    page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                    cscec.getVisible(page,"//td/div[text()='0210']").dblclick()

                    pageTable.clickInputQuery2(1,"*成本费用事项")
                    cscec.getVisible(page,"//input[@placeholder='请输入查询关键字']").fill("021000600010")
                    page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                    cscec.getVisible(page,"//td/div[text()='021000600010']").dblclick()

                    pageTable2=cscec.cscecTable(page,"收款账号")
                    pageTable2.reIndex()
                    pageTable2.fillInput(1,"*付款摘要",row["摘要"])
                  
                    
                    if pd.isna(row["附件"])==False:
                        cscec.uploadAttachment(page,row["附件"])
                    
                    cscec.chooseIfPaper(page,False) #选择是否含纸质附件
                    
                    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
                    cscec.clickDigalog(page)
                    cscec.getVisible(page,"//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click()
                    cscec.clickDigalog(page)
                    innerText=page.locator("//div[text()='系统提示']/parent::div/parent::div/parent::div").text_content()
                    if '资金计划控制' in innerText:
                        cscec.clickDigalog(page) #后两个不一定有
                        cscec.clickDigalog(page)
                    cscec.clickDigalog(page)

                    cscec.closeTab(page)
                    cscec.closeTab(page)
                conn.updateData("劳务派遣认领","是否分割","本行执行完毕",i+1)
        print("完成任务")

    def payUnionfunds(self):
        Br=browser.myBrowser("cscec")
        page=Br.page
        ws=excel.myBook().sheet("工会结转")
        table=ws.table("表1")
        startCount=int(ws.Cells(1,2).Value)
        for i in range(startCount+1,table.MaxRow+1):
            cscec.changeProjectCscec(page,table.getValue(i,"组织机构"),table.getValue(i,"项目名称"))
            cscec.toFunction(page,"报账系统","员工薪酬及费用","工会经费及教育经费上缴")
            cscec.fillLalbel_input(page,"事 由",table.getValue(i,"事由"))
            cscec.Lablechoose(page,"业务类型","请输入查询关键字","业务类型","同法人结转支付(经费)")
            cscec.Lablechoose(page,"付款计划项目","请输入查询关键字","付款计划项目","0030001000200010")
            cscec.chooseIfPaper(page,False)
            theTable1=cscec.cscecTable(page,"工资项名称")
            theTable1.appendRow()
            theTable1.clickInputQuery(1,"*工资项编号")
            cscec.locatorDigalog(page,"帮助字典").get_by_text("工会经费").dblclick()
            theTable1.fillInput(1,"金额",str(table.getValue(i,"金额")))
            
            theTable2=cscec.cscecTable(page,"收款方项目")
            theTable2.appendRow()
            time.sleep(0.5)
            theTable2.reIndex()
            theTable2.clickInputQuery(1,"*客商(员工)编号")
            cscec.getVisible(page,"//input[@placeholder='请输入查询的内容如：单位编号、单位名称。']").fill(table.getValue(i,"收款方编码"))
            tryInt=20
            while tryInt>0:
                try:
                    cscec.getVisible(page,"//img[contains(@src,'查询_chaxun')]/parent::div[@role='button']").click()
                    cscec.locatorDigalog(page,"帮助").locator("//*[text()='中建三局安装工程有限公司南方大区']").dblclick(timeout=1000)
                    tryInt=-1
                except:
                    tryInt=tryInt-1
                    time.sleep(0.1)
                    pass
            theTable2.clickInputQuery(1,"*收款方项目")
            cscec.dialogInput(page,table.getValue(i,"收款方项目"))
            theTable2.reIndex()
            theTable2.fillInput(1,"*付款摘要","南方薪酬工会经费划转至机关")
            cscec.getVisible(page,"//span[text()='保存']").click()
            cscec.clickDigalog(page,"提示")
            cscec.getVisible(page,"//span[text()='提交']").click()
            cscec.clickDigalog(page,"处理意见")
            cscec.clickDigalog(page,"提示")
            cscec.closeTab(page)
            ws.Cells(1,2).Value=ws.Cells(1,2).Value+1

def writeData( ):
    conn=excelDB()
    file_path = fileui.select_file()
    if file_path:
        df = pd.read_excel(file_path)
        df.to_sql("劳务派遣认领", conn.conn, if_exists='replace', index=False)
        conn.conn.close()

def queryData():
    conn=excelDB()
    try:
        df = pd.read_sql("SELECT * FROM 劳务派遣认领", conn.conn)
    except:
        df = pd.DataFrame(columns=["序号", "是否分割", "单位", "项目名称", "摘要","事由","附件"])
    conn.conn.close()
    file_dir = fileui.select_directory()
    df.to_excel(file_dir + "/劳务派遣认领.xlsx", index=False)

def laborDispatchTemplate():
    conn=excelDB()
    try:
        df = pd.read_sql("SELECT * FROM 劳务派遣认领", conn.conn)
    except:
        df = pd.DataFrame(columns=["序号", "是否分割", "单位", "项目名称", "摘要","事由","附件"])
    conn.conn.close()
    return {"劳务派遣认领":[df.columns.tolist()]+df.values.tolist()}

def laborDispatchUpdate(data):
    conn=excelDB()
    data=data["currentData"]["劳务派遣认领"]
    processed_columns = [f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(data[0])]
    df = pd.DataFrame(columns=processed_columns,data=data[1:])
    df.to_sql("劳务派遣认领", conn.conn, if_exists='replace', index=False)
    conn.conn.close()
  
def main():
    salary().confirmSalary()
