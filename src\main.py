import sys
import importlib
import threading
import time
import os
from pathlib import Path
from src.Gui.FletGui.welcome_screen import WelcomeScreen
import src.base.settings as setting
import requests
from src.web.http import start_server  # 导入服务器启动函数

import flet as ft
version="2.3.5"

# 全局变量用于跟踪软件启动时间
app_start_time = time.time()
latest_version = version  # 用于存储最新版本信息
# Add project root to sys.path to allow imports from src
# Assuming this main.py is at src/Gui/FletGui2/main.py
# Project root is 3 levels up from this file's directory
PROJECT_ROOT = Path(__file__).resolve().parents[3]
#sys.path.insert(0, str(PROJECT_ROOT))

from src.Gui.FletGui2.views import ui_constants as C
from src.Gui.FletGui2.views import home_view

# --- Global State for Message Manager & UI Refs ---
message_manager_visible = True
messages = []
current_message_content = "系统初始化中..."  # Store the full message content
message_display_ref = ft.Ref[ft.TextField]()  # Changed from ListView to TextField for better copy-paste
view_container_ref = ft.Ref[ft.Container]()      # Main content area for switching views
header_ref = ft.Ref[ft.Container]()
message_manager_content_ref = ft.Ref[ft.Container]()
shared_queue = None  # Will be set after importing callProcess
queue_monitor_thread = None  # Thread for monitoring shared_queue
# -- New globals for recent activity tracking --
recent_activities = []  # List of tuples (message, timestamp, color)
recent_activity_column_ref = ft.Ref[ft.Column]()
# -- Globals for software info widget --
software_info_refs = {
    'runtime_text': ft.Ref[ft.Text](),
    'version_text': ft.Ref[ft.Text]()
}

# Helper to record recent activity based on clicked function/view
def record_recent_activity(activity_name: str, color: str = C.SUCCESS_COLOR):
    """Add a new activity entry (triggered by view click) and refresh widget."""
    timestamp = time.strftime("%H:%M:%S")
    recent_activities.append((activity_name, timestamp, color))
    if len(recent_activities) > 20:
        recent_activities.pop(0)

    # Update UI column if present
    if recent_activity_column_ref.current:
        recent_activity_column_ref.current.controls.clear()
        for msg, ts, clr in reversed(recent_activities[-3:]):
            recent_activity_column_ref.current.controls.append(
                ft.Row([
                    ft.Icon(ft.icons.CHECK_CIRCLE, color=clr, size=12),
                    ft.Column([
                        ft.Text(msg, size=9, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                        ft.Text(ts, size=8, color=ft.colors.with_opacity(0.7, C.TEXT_COLOR), font_family=C.FONT_CONSOLAS),
                    ], spacing=1, expand=True),
                ], spacing=6)
            )
        recent_activity_column_ref.current.update()

def get_app_runtime():
    """获取软件运行时长"""
    runtime_seconds = time.time() - app_start_time
    hours = int(runtime_seconds // 3600)
    minutes = int((runtime_seconds % 3600) // 60)
    seconds = int(runtime_seconds % 60)

    if hours > 0:
        return f"{hours}h {minutes}m"
    elif minutes > 0:
        return f"{minutes}m {seconds}s"
    else:
        return f"{seconds}s"

def update_software_info():
    """定期更新软件信息显示"""
    def update_loop():
        while True:
            try:
                # 更新运行时长
                if software_info_refs['runtime_text'].current:
                    software_info_refs['runtime_text'].current.value = get_app_runtime()
                    software_info_refs['runtime_text'].current.update()

                # 更新版本状态
                if software_info_refs['version_text'].current:
                    version_status = "最新版本" if latest_version == version else f"最新版本 {latest_version}"
                    version_color = C.SUCCESS_COLOR if latest_version == version else C.ACCENT_COLOR

                    software_info_refs['version_text'].current.value = version_status
                    software_info_refs['version_text'].current.color = version_color
                    software_info_refs['version_text'].current.update()

                time.sleep(5)  # 每5秒更新一次
            except Exception as e:
                # 如果更新失败，等待一段时间后继续尝试
                time.sleep(10)

    # 启动后台线程
    threading.Thread(target=update_loop, daemon=True).start()

# Widget creation functions
def create_system_status_widget():
    """Create a compact system status widget for the bottom panel."""
    return ft.Container(
        content=ft.Column(
            [
                ft.Row(
                    [
                        ft.Icon(ft.icons.COMPUTER_ROUNDED, color=C.ACCENT_COLOR, size=18),
                        ft.Text("系统状态", size=14, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON),
                    ],
                    spacing=6,
                ),
                ft.Divider(height=1, color=ft.colors.with_opacity(0.8, C.ACCENT_COLOR)),
                ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Icon(ft.icons.CIRCLE, color=C.SUCCESS_COLOR, size=10),
                                ft.Text("RPA服务启动正常", size=10, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                            ],
                            spacing=6,
                        ),
                        ft.Row(
                            [
                                ft.Icon(ft.icons.CIRCLE, color=C.SUCCESS_COLOR, size=10),
                                ft.Text("数据库连接正常", size=10, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                            ],
                            spacing=6,
                        ),
                        ft.Row(
                            [
                                ft.Icon(ft.icons.CIRCLE, color=C.SUCCESS_COLOR, size=10),
                                ft.Text("web服务启动正常", size=10, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                            ],
                            spacing=6,
                        ),
                    ],
                    spacing=4,
                ),
            ],
            spacing=6,
        ),
        bgcolor=C.SECONDARY_COLOR,
        border_radius=10,
        padding=12,
        border=ft.border.all(1, ft.colors.with_opacity(0.8, C.ACCENT_COLOR)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=2,
            color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
            offset=ft.Offset(0, 1),
        ),
        height=160,  # Fixed height for consistent bottom panel
    )

def create_quick_stats_widget():
    """Create a compact software info widget for the bottom panel."""
    # 创建动态更新的运行时长文本控件
    runtime_text = ft.Text(
        get_app_runtime(),
        size=10,
        weight=ft.FontWeight.BOLD,
        color=C.SUCCESS_COLOR,
        font_family=C.FONT_CONSOLAS,
        ref=software_info_refs['runtime_text']
    )

    # 创建版本状态文本
    version_status = "最新版本" if latest_version == version else f"最新版本 {latest_version}"
    version_color = C.SUCCESS_COLOR if latest_version == version else C.ACCENT_COLOR

    version_text = ft.Text(
        version_status,
        size=10,
        weight=ft.FontWeight.BOLD,
        color=version_color,
        font_family=C.FONT_CONSOLAS,
        ref=software_info_refs['version_text']
    )

    return ft.Container(
        content=ft.Column(
            [
                ft.Row(
                    [
                        ft.Icon(ft.icons.INFO_ROUNDED, color=C.ACCENT_COLOR, size=18),
                        ft.Text("软件信息", size=14, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON),
                    ],
                    spacing=6,
                ),
                ft.Divider(height=1, color=ft.colors.with_opacity(0.8, C.ACCENT_COLOR)),
                ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Text("当前版本:", size=10, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                                ft.Text(f"V{version}", size=10, weight=ft.FontWeight.BOLD, color=C.SUCCESS_COLOR, font_family=C.FONT_CONSOLAS),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            [
                                ft.Text("版本状态:", size=10, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                                version_text,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            [
                                ft.Text("运行时长:", size=10, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                                runtime_text,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                    ],
                    spacing=4,
                ),
            ],
            spacing=6,
        ),
        bgcolor=C.SECONDARY_COLOR,
        border_radius=10,
        padding=12,
        border=ft.border.all(1, ft.colors.with_opacity(0.8, C.ACCENT_COLOR)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=2,
            color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
            offset=ft.Offset(0, 1),
        ),
        height=160,  # Fixed height for consistent bottom panel
    )

def create_recent_activity_widget():
    """Create a compact recent activity widget for the bottom panel. Listens to global recent_activities."""

    # Helper to build activity rows
    def _build_activity_rows():
        rows: list[ft.Control] = []
        # Show up to the latest 3 activities (most recent first)
        for msg, ts, color in reversed(recent_activities[-3:]):
            rows.append(
                ft.Row(
                    [
                        ft.Icon(ft.icons.CHECK_CIRCLE, color=color, size=12),
                        ft.Column(
                            [
                                ft.Text(msg, size=9, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                                ft.Text(ts, size=8, color=ft.colors.with_opacity(0.7, C.TEXT_COLOR), font_family=C.FONT_CONSOLAS),
                            ],
                            spacing=1,
                            expand=True,
                        ),
                    ],
                    spacing=6,
                )
            )
        if not rows:
            rows.append(ft.Text("暂无最近活动", size=9, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS))
        return rows

    # Column that will be updated when activities change
    activity_column = ft.Column(_build_activity_rows(), spacing=6, ref=recent_activity_column_ref)

    return ft.Container(
        content=ft.Column(
            [
                ft.Row(
                    [
                        ft.Icon(ft.icons.HISTORY_ROUNDED, color=C.ACCENT_COLOR, size=18),
                        ft.Text("最近活动", size=14, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON),
                    ],
                    spacing=6,
                ),
                ft.Divider(height=1, color=ft.colors.with_opacity(0.8, C.ACCENT_COLOR)),
                activity_column,
            ],
            spacing=6,
        ),
        bgcolor=C.SECONDARY_COLOR,
        border_radius=10,
        padding=12,
        border=ft.border.all(1, ft.colors.with_opacity(0.8, C.ACCENT_COLOR)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=2,
            color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
            offset=ft.Offset(0, 1),
        ),
        height=160,  # Fixed height for consistent bottom panel
    )


def creatMainPage(page: ft.Page):
    page.vertical_alignment = ft.MainAxisAlignment.START
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.bgcolor = C.PRIMARY_COLOR
    page.padding = 0
    page.fonts = {
        C.FONT_ORBITRON: f"https://fonts.googleapis.com/css2?family={C.FONT_ORBITRON.replace(' ', '+')}:wght@400;700&display=swap",
        C.FONT_CONSOLAS: "Consolas, monaco, monospace"
    }
    
    # Create window control buttons
    def create_window_button(icon, on_click, bg_hover_color):
        return ft.Container(
            content=ft.Icon(icon, size=16, color=C.TEXT_COLOR),
            width=40,
            height=30,
            on_click=on_click,
            ink=True,
            border_radius=0,
            bgcolor=ft.colors.TRANSPARENT,
            on_hover=lambda e: setattr(e.control, 'bgcolor', bg_hover_color if e.data == "true" else ft.colors.TRANSPARENT) or e.control.update(),
        )
    
    # Create title bar
    title_bar = ft.Container(
        content=ft.Row(
            [
                ft.WindowDragArea(
                    ft.Container(
                        ft.Row(
                            [
                                ft.Icon(ft.icons.ACCOUNT_BALANCE_WALLET, color=C.ACCENT_COLOR, size=20),
                                ft.Text("信小财 V3.0", color=C.TEXT_COLOR, font_family=C.FONT_ORBITRON, size=14, weight=ft.FontWeight.BOLD),
                            ],
                            spacing=10,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                            expand=True,
                        ),
                        padding=ft.padding.only(left=15),
                    ),
                    expand=True,
                ),
                create_window_button(
                    ft.icons.MINIMIZE_ROUNDED,
                    lambda _: page.window_minimized or page.window_minimized is True or setattr(page, 'window_minimized', True) or page.update(),
                    ft.colors.with_opacity(0.1, C.ACCENT_COLOR)
                ),
                create_window_button(
                    ft.icons.CLOSE,
                    lambda _: page.window_close(),
                    "#ff0000"
                ),
            ],
            spacing=0,
        ),
        height=30,
        bgcolor=C.SECONDARY_COLOR,
    )

    def add_message(text: str, color: str = C.TEXT_COLOR):
        """Add a message to the display - now updates the TextField directly"""
        global current_message_content
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {text}"

        # Add to messages list for backward compatibility
        messages.append(ft.Text(formatted_message, color=color, size=12, font_family=C.FONT_CONSOLAS))
        if len(messages) > 100:  # Increased limit for more message history
            messages.pop(0)

        # Update the current message content
        current_message_content = formatted_message
        update_message_display()

    def update_message_display():
        """Update the message display TextField"""
        if message_display_ref.current:
            message_display_ref.current.value = current_message_content
            message_display_ref.current.update()

    def update_message_log():
        """Legacy function for compatibility"""
        update_message_display()

    def start_queue_monitor():
        """Start monitoring shared_queue for messages"""
        global queue_monitor_thread, shared_queue

        def monitor_queue():
            while True:
                try:
                    if shared_queue and not shared_queue.empty():
                        message_data = shared_queue.get(timeout=0.1)
                        if isinstance(message_data, dict) and "消息" in message_data:
                            message_text = message_data["消息"]
                            # Update the global message content directly
                            global current_message_content
                            current_message_content = message_text
                            # Update the display
                            if message_display_ref.current:
                                message_display_ref.current.value = message_text
                                message_display_ref.current.update()
                except:
                    pass  # Continue monitoring even if there are errors
                time.sleep(0.1)

        if queue_monitor_thread is None or not queue_monitor_thread.is_alive():
            queue_monitor_thread = threading.Thread(target=monitor_queue, daemon=True)
            queue_monitor_thread.start()

    def update_ui():
        if message_manager_content_ref.current:
            message_manager_content_ref.current.visible = message_manager_visible
        if header_ref.current and header_ref.current.content and len(header_ref.current.content.controls) > 1:
            header_ref.current.content.controls[1].icon = ft.icons.MENU_OPEN_ROUNDED if message_manager_visible else ft.icons.MENU_ROUNDED
        # Update the page
        page.update()
        # Avoid logging message for UI update itself unless specifically needed
        # add_message(f"Message manager {"shown" if message_manager_visible else "hidden"}.")

    def toggle_message_manager(e):
        global message_manager_visible
        message_manager_visible = not message_manager_visible
        update_ui()
        add_message(f"Message manager {"shown" if message_manager_visible else "hidden"}.")

    def show_home_view(e=None): # Added e=None for consistent callback signature
        if view_container_ref.current and function_grid_container:
            # Create enhanced home screen layout with bottom widgets panel
            home_screen_layout = ft.Container(
                content=ft.Column(
                    [
                        # Top area with function grid (no scrolling)
                        ft.Container(
                            content=function_grid_container,
                            expand=True,
                            alignment=ft.alignment.center,
                            padding=ft.padding.symmetric(horizontal=20, vertical=15),
                        ),
                        # Bottom panel with widgets in a horizontal row
                        ft.Container(
                            content=ft.Row(
                                [
                                    # System status widget
                                    ft.Container(
                                        content=create_system_status_widget(),
                                        expand=True,
                                    ),
                                    # Quick stats widget
                                    ft.Container(
                                        content=create_quick_stats_widget(),
                                        expand=True,
                                    ),
                                    # Recent activity widget
                                    ft.Container(
                                        content=create_recent_activity_widget(),
                                        expand=True,
                                    ),
                                ],
                                spacing=15,
                                expand=False,
                            ),
                            height=200,  # Fixed height to prevent scrolling
                            padding=ft.padding.symmetric(horizontal=20, vertical=10),
                            bgcolor=ft.colors.with_opacity(0.4, C.SECONDARY_COLOR),
                            border_radius=ft.border_radius.only(top_left=12, top_right=12),
                            border=ft.border.only(top=ft.border.BorderSide(2, ft.colors.with_opacity(0.7, C.ACCENT_COLOR))),
                        ),
                    ],
                    expand=True,
                    spacing=0,
                ),
                expand=True,
                padding=ft.padding.all(0)
            )
            view_container_ref.current.content = home_screen_layout
            view_container_ref.current.update()
            add_message("Navigated to Home Screen", C.SUCCESS_COLOR)
        else:
            print("DEBUG: Critical refs not ready in show_home_view (view_container_ref, function_display_area_ref, or function_grid_container)")
            add_message("Error: UI components not ready for home screen.", C.ERROR_COLOR)

    def load_view(module_name: str, view_display_name: str, e=None):
        if view_container_ref.current: # Target the main view container
            try:
                full_module_path = "src.Gui."+f"FletGui2.views.{module_name}"
                view_module = importlib.import_module(full_module_path)

                if hasattr(view_module, "get_view"):
                    view_content = view_module.get_view(page, view_display_name, show_home_view)
                    view_container_ref.current.content = view_content # Load view into the main container
                    add_message(f"Loaded {view_display_name}", C.SUCCESS_COLOR)
                    record_recent_activity(view_display_name, C.SUCCESS_COLOR)
                else:
                    error_text = f"Error: '{module_name}' view has no get_view() function."
                    view_container_ref.current.content = ft.Text(error_text, color=C.ERROR_COLOR, font_family=C.FONT_CONSOLAS)
                    add_message(f"Error loading view '{module_name}'.", C.ERROR_COLOR)
            except ModuleNotFoundError:
                error_text = f"Error: View module '{module_name}' not found."
                view_container_ref.current.content = ft.Text(error_text, color=C.ERROR_COLOR, font_family=C.FONT_CONSOLAS)
                add_message(f"Could not find view module '{module_name}'.", C.ERROR_COLOR)
            except Exception as ex:
                error_text = f"An error occurred while loading {view_display_name}."
                view_container_ref.current.content = ft.Text(error_text, color=C.ERROR_COLOR, font_family=C.FONT_CONSOLAS)
                add_message(f"Error initializing {view_display_name}: {ex}", C.ERROR_COLOR)

            view_container_ref.current.update() # Update the main container
        else:
            add_message(f"Critical Error: Main view container (view_container_ref) is missing. Cannot load {view_display_name}.", C.ERROR_COLOR)

    # --- UI Components ---
    header = ft.Container(
        ref=header_ref,
        content=ft.Row(
            [
                ft.Row([ft.Text(f"信小财RPA财务机器人V{version}", size=28, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON),
                ft.TextButton(
                    "操作手册",
                    icon=ft.icons.WEB_ROUNDED,
                    url='https://q2t4357ads.feishu.cn/wiki/H2GHwBZjFiIkF4kKxFTclmTanTg?from=from_copylink'
                )]),
                ft.IconButton(
                    ft.icons.MENU_OPEN_ROUNDED if message_manager_visible else ft.icons.MENU_ROUNDED,
                    icon_color=C.ACCENT_COLOR, icon_size=30, tooltip="隐藏消息管理器", on_click=toggle_message_manager,
                ),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        ),
        bgcolor=C.SECONDARY_COLOR, padding=ft.padding.symmetric(horizontal=20, vertical=15),
        border=ft.border.only(bottom=ft.border.BorderSide(2, C.ACCENT_COLOR)),
        shadow=ft.BoxShadow(spread_radius=1, blur_radius=10, color=ft.colors.with_opacity(0.5, C.ACCENT_COLOR), offset=ft.Offset(0, 2))
    )

    function_grid_container = ft.Container(
        content=home_view.get_function_grid(on_click_callback=load_view),
        # width=700, # Removed width, will be controlled by parent container in show_home_view
        # padding=ft.padding.only(top=10, left=10, right=10, bottom=10), # Padding moved to wrapper in show_home_view
        alignment=ft.alignment.center # Ensure grid itself tries to center its content if not expanding fully
    )


    # This container will hold either the home screen (grid + display area) or a loaded view
    view_container = ft.Container(
        ref=view_container_ref,
        expand=True,
        # Initial content will be set by show_home_view when app starts
    )

    # Enhanced message display with copy-paste functionality
    def copy_message_content(e):
        """Copy message content to clipboard"""
        if message_display_ref.current and message_display_ref.current.value:
            page.set_clipboard(message_display_ref.current.value)
            add_message("消息内容已复制到剪贴板", C.SUCCESS_COLOR)

    def clear_message_display(e):
        """Clear the message display"""
        global current_message_content
        current_message_content = "消息已清空"
        if message_display_ref.current:
            message_display_ref.current.value = current_message_content
            message_display_ref.current.update()

    # Enhanced message display with context menu
    def show_context_menu(e):
        """Show context menu for message operations"""
        context_menu = ft.MenuBar(
            controls=[
                ft.SubmenuButton(
                    content=ft.Text("操作"),
                    controls=[
                        ft.MenuItemButton(
                            content=ft.Text("复制全部内容"),
                            leading=ft.Icon(ft.icons.COPY_ALL),
                            on_click=copy_message_content,
                        ),
                        ft.MenuItemButton(
                            content=ft.Text("清空显示"),
                            leading=ft.Icon(ft.icons.CLEAR),
                            on_click=clear_message_display,
                        ),
                        ft.MenuItemButton(
                            content=ft.Text("刷新显示"),
                            leading=ft.Icon(ft.icons.REFRESH),
                            on_click=lambda e: update_message_display(),
                        ),
                    ],
                ),
            ],
        )
        return context_menu

    message_display = ft.TextField(
        ref=message_display_ref,
        value=current_message_content,
        multiline=True,
        min_lines=20,
        max_lines=None,  # Allow unlimited lines to prevent truncation
        read_only=True,
        expand=True,
        text_style=ft.TextStyle(
            size=12,
            font_family=C.FONT_CONSOLAS,
            color=C.TEXT_COLOR,
        ),
        border_color=ft.colors.with_opacity(0.5, C.ACCENT_COLOR),
        focused_border_color=C.ACCENT_COLOR,
        bgcolor=ft.colors.with_opacity(0.5, C.SECONDARY_COLOR),
        border_radius=8,
        content_padding=ft.padding.all(12),
        # Enable text selection for copy operations
        selection_color=ft.colors.with_opacity(0.5, C.ACCENT_COLOR),
        # Add keyboard shortcuts
        on_submit=lambda e: None,  # Prevent default behavior
        # Add right-click context menu support
        tooltip="右键点击查看更多操作选项\nCtrl+A 全选\nCtrl+C 复制选中内容",
    )

    # Create control buttons for the message display
    def stop_execution(e):
        import src.Gui.callProcess as callF
        callF.thisProcess.terminate()

    message_controls = ft.Row(
        [   ft.IconButton(
                icon=ft.icons.FOLDER_OPEN_ROUNDED,
                tooltip="打开文件夹",
                icon_color=C.ACCENT_COLOR,
                on_click=lambda e: os.startfile(setting.PATH_DATA),  # 替换为您的实际URL,
                icon_size=18,
            ),
            ft.IconButton(
                icon=ft.icons.STOP_ROUNDED,
                tooltip="快速停止执行",
                icon_color=ft.colors.RED_500,
                on_click=stop_execution,
                icon_size=18,
            ),
            ft.IconButton(
                icon=ft.icons.COPY_ROUNDED,
                tooltip="复制消息内容",
                icon_color=C.ACCENT_COLOR,
                on_click=copy_message_content,
                icon_size=18,
            ),
            ft.IconButton(
                icon=ft.icons.CLEAR_ROUNDED,
                tooltip="清空消息显示",
                icon_color=C.TEXT_COLOR,
                on_click=clear_message_display,
                icon_size=18,
            ),
            ft.IconButton(
                icon=ft.icons.REFRESH_ROUNDED,
                tooltip="刷新消息显示",
                icon_color=C.ACCENT_COLOR,
                on_click=lambda e: update_message_display(),
                icon_size=18,
            ),
        ],
        alignment=ft.MainAxisAlignment.END,
        spacing=5,
    )

    message_manager_content = ft.Container(
        ref=message_manager_content_ref,
        content=ft.Column(
            [
                ft.Row([ft.Icon(ft.icons.MESSAGE_ROUNDED, color=C.ACCENT_COLOR), ft.Text("消息日志", size=16, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON)], spacing=10),
                ft.Divider(height=1, color=C.ACCENT_COLOR),
                message_controls,  # Add control buttons
                message_display,   # Use the new enhanced message display
            ],
            expand=True,
            spacing=8,
        ),
        width=300, bgcolor=C.SECONDARY_COLOR, padding=15,  # Increased width for better readability
        border=ft.border.only(left=ft.border.BorderSide(2, C.ACCENT_COLOR)),
        shadow=ft.BoxShadow(spread_radius=1, blur_radius=10, color=ft.colors.with_opacity(0.5, C.ACCENT_COLOR), offset=ft.Offset(-2, 0)),
        visible=message_manager_visible,
        animate_size=ft.animation.Animation(300, "easeOutCubic"),
        animate_opacity=ft.animation.Animation(300, "easeOutCubic")
    )

    layout = ft.Column(
        [
            header,
            ft.Row(
                [view_container, message_manager_content], # view_container is now the main content holder
                expand=True,
                vertical_alignment=ft.CrossAxisAlignment.STRETCH
            )
        ],
        expand=True, spacing=0
    )

    # Initial setup
    page.add(layout)
    show_home_view() # Display initial welcome screen content
    add_message("System Initialized. Welcome to FIP RPA Control Terminal.", C.SUCCESS_COLOR)

    # Initialize shared_queue connection after callProcess is imported
    def init_shared_queue():
        global shared_queue
        try:
            import src.Gui.callProcess as callProcess
            shared_queue = callProcess.shared_queue
            start_queue_monitor()
            add_message("消息队列连接成功，开始监控进程输出", C.SUCCESS_COLOR)
        except Exception as e:
            add_message(f"消息队列连接失败: {e}", C.ERROR_COLOR)

    # Start queue monitoring in a separate thread to avoid blocking
    threading.Thread(target=init_shared_queue, daemon=True).start()

    # Start software info update thread
    update_software_info()

    page.update()

def check_registration_and_version(page: ft.Page):
        global latest_version
        try:
            server_process = start_server() #先启动fastapi服务 主程序结束时，服务器进程会自动关闭（因为设置了daemon=True）
        except Exception as e:
            # 创建注册弹窗
            register_dialog = ft.AlertDialog(
                title=ft.Text("启动FastAPI服务器失败"),
                content=ft.Text(str(e)),
                actions=[
                    ft.TextButton("知道了", on_click=lambda e: (setattr(page.dialog, "open", False), page.update()))
                ]
            )
            page.dialog = register_dialog
            page.dialog.open = True
            page.update()
        # 版本检查逻辑
        import src.Gui.callProcess as callProcess #导入开始创建函数进程
        from src.Gui.FletGui.register_dialog import RegisterDialog
        from src.Gui.register import check_register_status,updateRegister,initLock

        # Initialize shared_queue globally
        #global shared_queue
        #shared_queue = callProcess.shared_queue
        initLock()
        try:
            updateRegister()
            response = requests.get('https://cscec3b-fip.hb11oss.ctyunxs.cn/version.txt')
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                items = content.replace('\r\n', '\n').split('\n')
                # 解析为字典
                result = {}
                for item in items:
                    # 按等号分割，只分割一次
                    key, value = item.split('=', 1)
                    result[key] = value
                remote_version = result.get("version", "")
                remote_desc = result.get("description", "")
                # 更新全局最新版本变量
                latest_version = remote_version if remote_version else version
                # 仅比较数字部分
                def version_tuple(v):
                    try:
                        return tuple(int(x) for x in v.lstrip('vV').split('.') if x.isdigit())
                    except (ValueError, AttributeError) as e:
                        return (0,)

                local_ver = version_tuple(version)
                remote_ver = version_tuple(remote_version)
                if remote_ver > local_ver:
                    dlg = ft.AlertDialog(
                        title=ft.Text(f"可获取的新版本#{remote_version}"),
                        content=ft.Text(remote_desc or "新版本可用"),
                        actions=[
                            ft.TextButton("知道了", on_click=lambda e: (setattr(page.dialog, "open", False), page.update()))
                        ]
                    )
                    page.dialog = dlg
                    page.dialog.open = True
                    page.update()
        except requests.exceptions.RequestException as e:
            print(f"版本检查网络请求失败: {str(e)}")
        except Exception as e:
            print(f"版本检查发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
        
        if not check_register_status():
            # 创建注册弹窗
            register_dialog = RegisterDialog(on_register_success=lambda: page.update())
            page.dialog = register_dialog
            page.dialog.open = True
            page.update()


def main(page: ft.Page):
    page.title = f"信小财 V{version}"
    page.window.icon = setting.CACHE_PATH+"/rpa.ico"
    # Configure window for custom title bar
    #page.window_title_bar_hidden = True
    #ppage.window_title_bar_buttons_hidden = True
    #page.window_frameless = True
    #page.window_bgcolor = ft.Colors.TRANSPARENT
    
    page.locale_configuration = ft.LocaleConfiguration(
        supported_locales=[
            ft.Locale("zh", "CN", "Hans"),
        ],
        current_locale=ft.Locale("zh", "CN", "Hans"),
    )
    page.window.center()
    # 显示欢迎界面
    welcome_if=True
    if welcome_if:
        welcome_screen = WelcomeScreen(1300)
        def on_welcome_complete():
            # 欢迎界面完成后，显示主界面
            page.clean()  
            creatMainPage(page)
        welcome_screen.show(page, on_welcome_complete)
    else:
        creatMainPage(page)
    check_registration_and_version(page)
    

if __name__ == "__main__":
    ft.app(target=main, assets_dir=str(PROJECT_ROOT / "src" / "Gui" / "FletGui2" / "assets"), view=ft.AppView.FLET_APP)
