#!/usr/bin/env python3
"""
图标设置工具
用于检查和设置rpa.ico图标文件
"""

import os
import shutil
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

import src.base.settings as settings

def check_icon_file():
    """检查图标文件是否存在"""
    ico_path = os.path.join(settings.CACHE_PATH, "rpa.ico")
    print(f"检查图标文件: {ico_path}")
    
    if os.path.exists(ico_path):
        print("✓ 图标文件存在")
        file_size = os.path.getsize(ico_path)
        print(f"  文件大小: {file_size} 字节")
        return True
    else:
        print("✗ 图标文件不存在")
        return False

def setup_cache_directory():
    """设置缓存目录"""
    print(f"设置缓存目录: {settings.CACHE_PATH}")
    os.makedirs(settings.CACHE_PATH, exist_ok=True)
    print("✓ 缓存目录已创建")

def copy_icon_from_source(source_path):
    """从指定路径复制图标文件"""
    if not os.path.exists(source_path):
        print(f"✗ 源图标文件不存在: {source_path}")
        return False
    
    dest_path = os.path.join(settings.CACHE_PATH, "rpa.ico")
    try:
        shutil.copy2(source_path, dest_path)
        print(f"✓ 图标文件已复制到: {dest_path}")
        return True
    except Exception as e:
        print(f"✗ 复制图标文件失败: {e}")
        return False

def create_default_icon():
    """创建一个简单的默认图标文件（如果没有其他选择）"""
    # 这里可以创建一个简单的ICO文件或者从网络下载
    print("创建默认图标功能待实现")
    return False

def main():
    """主函数"""
    print("=== 图标设置工具 ===")
    print(f"CACHE_PATH: {settings.CACHE_PATH}")
    
    # 1. 设置缓存目录
    setup_cache_directory()
    
    # 2. 检查图标文件
    if check_icon_file():
        print("图标文件已就绪")
        return
    
    # 3. 尝试从可能的位置查找图标文件
    possible_paths = [
        os.path.join(settings.PATH_EXE, "rpa.ico"),
        os.path.join(settings.PATH_DATA, "rpa.ico"),
        os.path.join(settings.PATH_STATIC, "rpa.ico"),
        os.path.join(os.path.dirname(settings.PATH_EXE), "rpa.ico"),
        "rpa.ico"  # 当前目录
    ]
    
    print("\n查找图标文件...")
    for path in possible_paths:
        print(f"检查: {path}")
        if os.path.exists(path):
            print(f"✓ 找到图标文件: {path}")
            if copy_icon_from_source(path):
                print("图标设置完成")
                return
    
    print("\n✗ 未找到图标文件")
    print("请手动将rpa.ico文件放置到以下位置之一:")
    for path in possible_paths:
        print(f"  - {path}")
    
    print(f"\n或者直接放置到: {os.path.join(settings.CACHE_PATH, 'rpa.ico')}")

if __name__ == "__main__":
    main()
