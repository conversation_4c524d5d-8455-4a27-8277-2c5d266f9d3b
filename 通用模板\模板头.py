import os
import sys

sys.path.append(".")
from playwright.sync_api import Playwright, sync_playwright,Page,Locator
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.dop as dop
import src.base.settings as settings
import re
import time
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>

def get_month_based_on_date() -> str:
    """
    根据当前日期判断返回月份
    如果当前日期在25号之后(不包括25号)，则返回下个月，否则返回当月
    返回格式为：xxxx年xx月
    """
    today = datetime.now()
    
    # 如果当前日期大于25号
    if today.day > 25:
        # 获取下个月的第一天
        if today.month == 12:
            next_month = today.replace(year=today.year + 1, month=1, day=1)
        else:
            next_month = today.replace(month=today.month + 1, day=1)
        month_str = next_month.strftime('%Y年%m月')
    else:
        month_str = today.strftime('%Y年%m月')
    
    return month_str


with sync_playwright() as playwright:
    browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
    default_context = browser.contexts[0]
    page=cscec.switch_to_page(default_context,"中国建筑")
    predictTable=cscec.cscecTable(page,"收支类型")
    predictTable.reIndex()
    predictTable.fillInput(1,"年初数","1600") #收入
    predictTable.fillInput(1,"期初金额","0") #收入
    predictTable.fillInput(7,"年初数","1600")
    predictTable.fillInput(7,"期初金额","0") #收入
    predictTable.fillInput(8,"年初数","1500") #成本
    predictTable.fillInput(8,"期初金额","0") #收入
    predictTable.click(1,"收支类型")
   


        
 



    

