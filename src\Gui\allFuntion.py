import time
from src.Gui.register import check_register_status
import src.utils.Excel.excel as excel
import src.base.settings as settings
import src.base.cache as cache
from src.utils.DB.mainDB import mainDB


from typing import Callable, Any
import importlib
from datetime import datetime

# 定义映射表
FUNCTION_MAP = {
    "软件维护":("src.base.installerCheck","main"),
    "查询数据库":("src.utils.DB.updateDb","downloadFromDb"),
    "更新数据库":("src.utils.DB.updateDb","updateToDb"),
    "自动制证": ("src.utils.fipQuickFill.自动制证", "main"),
    "中台单据导出": ("src.utils.fipQuickFill.制证后中台单据批量下载", "autodownload"),
    "批量打印单据": ("src.utils.fipQuickFill.printBatch", "batchPrint"),
    "登记区块链台账": ("src.utils.fipQuickFill.区块链发票整理台账", "main"),
    "认领外包工资": ("src.utils.fipQuickFill.salary", "confirmSalary"),

    "拉取制证日志":("src.utils.DB.midIntrSQLiteDB","queryAutoCertification"),
    "清空制证日志":("src.utils.DB.midIntrSQLiteDB","clearAutoCertification"),
    
    "执行档案成册":("src.utils.fipQuickFill.fileCompilation","main"),
    "查询凭证数量":("src.utils.fipQuickFill.fileCompilation","queryVoucherCount"),
    "更新档案模版":("src.utils.fipQuickFill.fileCompilation","uploadFile"),
    "查询档案模版":("src.utils.fipQuickFill.fileCompilation","downloadFile"),
    "初始化收支台账模版":("src.utils.dopReport.moneyIOLedger","getTamplate"), #财商收支台账部分
    "更新收支台账模版":("src.utils.dopReport.moneyIOLedger","queryData"),
    "查询收支台账模版":("src.utils.dopReport.moneyIOLedger","writeData"),
    "上传收支台账":("src.utils.dopReport.moneyIOLedger","main"),
    "初始化财商计划模版":("src.utils.dopReport.stupidPlan","createTemplate"), #财商计划部分
    "更新财商计划模版":("src.utils.dopReport.stupidPlan","queryData"),
    "查询财商计划数量":("src.utils.dopReport.stupidPlan","writeData"),
    "上传财商计划":("src.utils.dopReport.stupidPlan","main"),
    "获取特殊支付模版":("src.utils.dopReport.specialPayment","queryData"), #特殊支付部分
    "更新特殊支付模版":("src.utils.dopReport.specialPayment","writeData"),
    "批量特殊支付":("src.utils.dopReport.specialPayment","main"),
    "查询商务推送模版":("src.utils.dopReport.businessPush","queryData"), #商务推送
    "更新商务推送模版":("src.utils.dopReport.businessPush","writeData"),
    "批量商务推送":("src.utils.dopReport.businessPush","main"),

    "导出一体化预付款及保证金台账重新开始":("src.utils.fundCapital.downloadLedger","main"), #预付款及保证金台账同步
    "导出一体化预付款及保证金台账失败继续":("src.utils.fundCapital.downloadLedger","main2"), #预付款及保证金台账同步

    "综合稽核":("src.utils.closeAccountingPeriod.audit","main"), #综合稽核
    "科目余额表检查明细账":("src.utils.closeAccountingPeriod.audit","main2"), #科目余额表检查明细账

    "一体化存量余额导出":("src.utils.fundCapital.自动查询资金余额","main"), #导出一体化资金余额

    "查询后台结账模版":("src.utils.closeAccountingPeriod.obtainData","queryData"),
    "更新后台结账模版":("src.utils.closeAccountingPeriod.obtainData","writeData"),

    "累计过账数据获取":("src.utils.closeAccountingPeriod.SAP结账获取已过账数据","autofill"),

    "查询劳务派遣认领模版":("src.utils.fipQuickFill.salary","queryData"),
    "更新劳务派遣认领模版":("src.utils.fipQuickFill.salary","writeData"),
    "执行劳派费用分割认领":("src.utils.fipQuickFill.salary","main"),

    "执行小额税金调整":("src.utils.tax.taxAdjustment","main"),#小额税金调整
    "查询小额税金调整模版":("src.utils.tax.taxAdjustment","queryData"),
    "更新小额税金调整模版":("src.utils.tax.taxAdjustment","writeData"),

    "查询在途单据数据库":("src.utils.fundCapital.paymentInTransitQuery","queryData"),
    "更新在途单据数据库":("src.utils.fundCapital.paymentInTransitQuery","writeData"),
    "同步一体化在途单据":("src.utils.fundCapital.paymentInTransitQuery","main"),

    "内部确认":("src.utils.closeAccountingPeriod.quickInternalConfirm","main"), #快速内部确认

    "查询反向保理模版":("src.utils.fundCapital.reverseFactoring","queryData"),
    "更新反向保理模版":("src.utils.fundCapital.reverseFactoring","writeData"),
    "执行反向保理提单":("src.utils.fundCapital.reverseFactoring","main"),
    "根据文件生成反向保理模板":("src.utils.fundCapital.reverseFactoring","generate_template_auto"),
    "直接生成反向保理模板":("src.utils.fundCapital.reverseFactoring","generate_template_direct"),

    "查询物资出库模版":("src.utils.fipQuickFill.财务一体化自动物资出库","queryData"),
    "更新物资出库模版":("src.utils.fipQuickFill.财务一体化自动物资出库","writeData"),
    "执行物资出库":("src.utils.fipQuickFill.财务一体化自动物资出库","main"),

    "补全期初数":("src.utils.financialStatement.complete","main"),
    "批量审核":("src.utils.financialStatement.check","main"),

    "插入单据脚本":("src.utils.Browser.openBrowserInTest","insertJS"),
    "重新插入数据库":("src.utils.DB.mainDB","reinsertData"),
    "打开配置文件":("src.utils.DB.mainDB","openConfigExcel"),
    "查询发票自动录入模版":("src.utils.fipQuickFill.财务一体化发票自动录入","queryData"),
    "更新发票自动录入模版":("src.utils.fipQuickFill.财务一体化发票自动录入","writeData"),
    "执行发票自动录入":("src.utils.fipQuickFill.财务一体化发票自动录入","main"),
    "查询支付提单模版":("src.utils.fipQuickFill.财务一体化自动调用付款单","queryData"),
    "更新支付提单模版":("src.utils.fipQuickFill.财务一体化自动调用付款单","writeData"),
    "执行支付提单":("src.utils.fipQuickFill.财务一体化自动调用付款单","main"),
    "查询物资结算模版":("src.utils.fipQuickFill.财务一体化自动物资结算2","queryData"),
    "更新物资结算模版":("src.utils.fipQuickFill.财务一体化自动物资结算2","writeData"),
    "执行物资结算":("src.utils.fipQuickFill.财务一体化自动物资结算2","main"),
    "查询分包结算模版":("src.utils.fipQuickFill.财务一体化自动分包结算","queryData"),
    "更新分包结算模版":("src.utils.fipQuickFill.财务一体化自动分包结算","writeData"),
    "执行分包结算":("src.utils.fipQuickFill.财务一体化自动分包结算","main"),

    "导出sap坏账":("src.utils.financialStatement.badDebtProvision","getTxt"),
    "执行坏账分析":("src.utils.financialStatement.badDebtProvision","main"),
    "获取财商数据差额":("src.utils.dopReport.compareData","main"),

    "获取科目对照远程参考":("src.utils.DB.updateDb","updateAccountMapTable"),
}

def callfunctionbyname(func_name: str, *args, **kwargs) -> Any:
    """
    根据字符串调用对应的模块和函数。
    
    :param func_name: 映射表中的字符串键
    :param args: 传递给目标函数的位置参数
    :param kwargs: 传递给目标函数的关键字参数
    :return: 目标函数的返回值
    """
    if func_name not in FUNCTION_MAP:
        raise KeyError(f"Function name '{func_name}' not found in the mapping table.")
    
    module_name, function_name = FUNCTION_MAP[func_name]
    
    # 动态导入模块
    module = importlib.import_module(module_name)
    
    # 获取函数对象并添加类型注解
    function: Callable[..., Any] = getattr(module, function_name)
    
    # 调用函数并返回结果
    return function(*args, **kwargs)

def allfunction(paramsDcit:dict):
    thePath=settings.PATH_DATA
    ifExpiration=check_register_status()
    #ifExpiration=True
    if paramsDcit.get("功能","")=="更新软件": #更新软件
        from src.base.update import update
        update.downloadFile()

    if ifExpiration:
        funcModule=paramsDcit.get("功能")
        moduleName=paramsDcit.get("模块")
        funcName=paramsDcit.get("函数")
        params=paramsDcit.get("参数")
        
        # 动态导入模块和函数
        if moduleName and funcName:
            try:
                module = __import__(f"src.{moduleName}", fromlist=[funcName])
                func = getattr(module, funcName)
                if params:
                    func(*params)
                else:
                    func()
            except Exception as e:
                print(f"执行功能失败: {e}") 
        if funcModule:
            if FUNCTION_MAP.get(funcModule):
                if params:
                    callfunctionbyname(funcModule,*params)
                else:
                    callfunctionbyname(funcModule)
            
            elif funcModule=="中台单据导出":
                import src.utils.fipQuickFill.制证后中台单据批量下载 as autodownload
                autodownload.autodownload()     
            elif funcModule=="特殊下载单据":
                import src.utils.fipQuickFill.特殊下载 as spdownload
                spdownload.spDownload()  

            elif funcModule=="批量打印单据":
                import src.utils.fipQuickFill.printBatch as printBatch
                printBatch.batchPrint()   

            elif funcModule=="登记区块链台账":
                import src.utils.fipQuickFill.区块链发票整理台账 as blockchain
                blockchain.main()

            elif funcModule=="sap明细数据导出":
                import src.utils.sapPublic.sapExport
                writepathToDB=src.utils.sapPublic.sapExport.exportMain("导出明细数据",paramsDcit["参数"][0],paramsDcit["参数"][1],False)
                from src.utils.DB.mainDB import mainDB
                mainDB().inserData(writepathToDB)  #将导出后数据写入数据库
                cache.wirteNowdate() 
                
            elif funcModule=="sap明细数据导出仅应付":
                import src.utils.sapPublic.sapExport 
                writepathToDB=src.utils.sapPublic.sapExport.exportMain("导出明细数据",paramsDcit["参数"][0],paramsDcit["参数"][1],True)
                from src.utils.DB.mainDB import mainDB
                mainDB().inserData(writepathToDB)  #将导出后数据写入数据库
                cache.wirteNowdate()
                
            elif funcModule=="按excel模版导出明细数据":
                import src.utils.sapPublic.sapExport
                src.utils.sapPublic.sapExport.exportMain("按excel模版导出明细数据")

            elif funcModule=="科目余额表导出SAP":
                from src.utils.DB.mainDB import mainDB
                mainDB().importBalanceToDB(paramsDcit["参数"][0],paramsDcit["参数"][1],paramsDcit["参数"][2])

            elif funcModule=="sap科目余额表导出":
                from src.utils.DB.mainDB import mainDB
                mainDB().importBalanceToDB(paramsDcit["参数"][1],paramsDcit["参数"][2],paramsDcit["参数"][0])   
                

            elif funcModule=="生成项目报表":
                import src.utils.DB.query
                src.utils.DB.query.getLedgerTest()

            elif funcModule=="重建数据结构":
                import src.utils.DB.creatDuckdbTable
                src.utils.DB.creatDuckdbTable.createTable()
            
            elif funcModule=="导出主数据" :
                import src.utils.DB.updateDb
                src.utils.DB.updateDb.updateMDtable()
            
            elif funcModule=="导出内部对账" :
                import src.utils.DB.updateDb
                src.utils.DB.updateDb.updateInternalTable()

            elif funcModule=="导出一体化合同台账" :
                import src.utils.DB.updateDb
                src.utils.DB.updateDb.updateFipContarctTable()
            
            elif funcModule=="邮件批量发送":
                import src.utils.sendmail.sendmail as sendmail
                sendmail.main()
            
            elif funcModule=="安全费计提":
                import src.utils.closeAccountingPeriod.财务一体化自动安全生产经费 as 自动安全生产经费
                自动安全生产经费.autofill()

            elif funcModule=="快速取数":
                import src.utils.closeAccountingPeriod.obtainData
                src.utils.closeAccountingPeriod.obtainData.main(paramsDcit["参数"][0],paramsDcit["参数"][1])

            elif funcModule=="SAP收入测算":
                import src.utils.closeAccountingPeriod.SAP结账自动生成匡算单 as 自动收入成本
                自动收入成本.autofill()
            elif funcModule=="SAP匡算确认":  
                import src.utils.closeAccountingPeriod.SAP结账自动确认匡算单 as SAP结账自动确认匡算单
                SAP结账自动确认匡算单.autofill()
            elif funcModule=="生成中台匡算":
                import src.utils.closeAccountingPeriod.财务一体化自动收入成本 as 中台结账自动生成匡算单
                中台结账自动生成匡算单.autofill()
            elif funcModule=="最终制证":
                import src.utils.closeAccountingPeriod.SAP结账自动生成匡算单结转凭证 as 中台结账自动生成匡算单结转凭证
                中台结账自动生成匡算单结转凭证.autofill()
            elif funcModule=="自动生成暂估单据":
                import src.utils.closeAccountingPeriod.财务一体化自动暂估 as 自动生成暂估单据
                自动生成暂估单据.autofill()
            elif funcModule=="划转管理费":
                import src.utils.closeAccountingPeriod.财务一体化自动总账工单划管理费 as transfer
                transfer.autofill()
            
            elif funcModule=="下载计划(重新开始)":
                from src.utils.fundCapital.财务一体化自动下载资金计划 import cachePlan
                objectPlan=cachePlan()
                objectPlan.dPlanThreeThread(paramsDcit["参数"][0],paramsDcit["参数"][1],True)
                objectPlan.ToExcel()
            
            elif funcModule=="下载计划(失败继续)":
                from src.utils.fundCapital.财务一体化自动下载资金计划 import cachePlan
                objectPlan=cachePlan()
                objectPlan.dPlanThreeThread(paramsDcit["参数"][0],paramsDcit["参数"][1],False)
                objectPlan.ToExcel()

            elif funcModule=="获取上传模版":
                from src.utils.fundCapital.财务一体化资金计划上报 import planUpload
                planUpload().getTemplate()
            
            elif funcModule=="资金计划批量上传":
                from src.utils.fundCapital.财务一体化资金计划上报 import planUpload
                planUpload().upload(paramsDcit["参数"][0])
            
            elif funcModule=="获取财商计划模版":
                from src.utils.dopReport.stupidPlan import stupidPlan
                stupidPlan().getTemplate()
            
            elif funcModule=="上传财商计划":
                from src.utils.dopReport.stupidPlan import stupidPlan
                stupidPlan().fillStupidPlan()
            
            elif funcModule=="更新分包分供台账":
                import src.utils.fundCapital.自动导出财务一体化合同台账 as fund4
                fund4.auto()

            elif funcModule=="获取收支台账模版":
                from src.utils.dopReport.moneyIOLedger import moneyIOLedger
                moneyIOLedger().getTamplate(paramsDcit["参数"][0],paramsDcit["参数"][1])
                
            elif funcModule=="上传收支台账":
                from src.utils.dopReport.moneyIOLedger import moneyIOLedger
                moneyIOLedger().fillmoneyLedger()

            elif funcModule=="打开chrome浏览器":
                import src.utils.Browser.openBrowserInTest as myBrowser
                myBrowser.openDebugModeBrowser("chrome")

            elif funcModule=="打开edge浏览器":
                import src.utils.Browser.openBrowserInTest as myBrowser
                myBrowser.openDebugModeBrowser("edge")

            elif funcModule=="打开基础配置表":
                excel.openExcel(settings.PATH_CONFIG+r"\配置文件.xlsx")
            
            elif funcModule=="自动物资结算":
                import src.utils.fipOther.财务一体化自动物资结算2
                src.utils.fipOther.财务一体化自动物资结算2.autoOperate()
            elif funcModule=="清除已登录信息":
                import src.utils.Browser.Browser as myBrowser
                myBrowser.clearCookies()

            elif funcModule=="打开网页配置表" :
                import webbrowser
                url = "http://127.0.0.1:8000/settings"  # 替换为你想打开的网址
                try:
                    # 使用默认浏览器打开网页
                    webbrowser.open(url)
                    print(f"已尝试使用默认浏览器打开: {url}")
                except Exception as e:
                    print(f"打开网页时出错: {e}")
            
            elif funcModule=="打开报表视图":
                import webbrowser
                url = "http://127.0.0.1:8000/budget-report"  # 替换为你想打开的网址
                try:
                    # 使用默认浏览器打开网页
                    webbrowser.open(url)
                    print(f"已尝试使用默认浏览器打开: {url}")
                except Exception as e:
                    print(f"打开网页时出错: {e}")

    
    else:
        print("注册到期，请先注册，本次启动功能失败")
        time.sleep(3)