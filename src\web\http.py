from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, Response
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import BaseModel
import subprocess
import threading
import time
import asyncio
import uvicorn
import sys
import logging
import traceback
import multiprocessing
import os
import src.base.settings as settings
import src.utils.closeAccountingPeriod.dataprocessing as dataprocessing
import requests

if not os.path.exists(settings.PATH_INTERNAL+"/web/assets"):
    os.makedirs(settings.PATH_INTERNAL+"/web/assets")
if not os.path.exists(settings.PATH_INTERNAL+"/web/static"):
    os.makedirs(settings.PATH_INTERNAL+"/web/static")
if not os.path.exists(settings.PATH_INTERNAL+"/web/js"):
    os.makedirs(settings.PATH_INTERNAL+"/web/js")

app = FastAPI()
# 添加CORS中间件

class NoCacheMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response: Response = await call_next(request)
        # 添加禁止缓存的响应头
        response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, proxy-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response

# 应用中间件
app.add_middleware(NoCacheMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件目录
app.mount("/assets", StaticFiles(directory=os.path.join(settings.PATH_INTERNAL,"web","assets")), name="assets")
app.mount("/static", StaticFiles(directory=os.path.join(settings.PATH_INTERNAL,"web","static")), name="static")
app.mount("/js", StaticFiles(directory=os.path.join(settings.PATH_INTERNAL,"web","js")), name="js")
# 挂载cache目录，用于访问图标等资源文件
app.mount("/cache", StaticFiles(directory=settings.CACHE_PATH), name="cache")

import src.web.quickly_query as quickly_query
import src.web.taxAndSalary as taxAndSalary
import src.web.ai as ai
import src.web.invoceAndMatiral as invoceAndMatiral
import src.web.allfunction as allfunction
import src.web.systemsyncView as systemsyncView

@app.get("/", response_class=HTMLResponse)
async def get_index():
    """
    返回 index.html 文件
    """
    try:
        index_path = os.path.join(settings.PATH_INTERNAL,"web","index.html")
        return FileResponse(index_path)
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail="index.html not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/{path:path}", response_class=HTMLResponse)  # {path:path}匹配所有多级路径
async def serve_vue_routes(path: str):
    try:
        # 始终返回index.html，由Vue的vue-router根据path解析对应组件
        index_path = os.path.join(settings.PATH_INTERNAL,"web","index.html")
        return FileResponse(index_path)
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="index.html not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/calculate")
async def root():
    return {"message": "Hello World"}

@app.post("/api/financial-analysis/data")
async def get_financial_analysis_data_all():
    try:
        from src.utils.DB.webReturn import getFinancialAnalysisDataAll
        return getFinancialAnalysisDataAll()
    except Exception as e:
        logging.error(f"Error in get_financial_analysis_data_all: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/capital-flow")
async def get_capital_flow_data(request: Request):
    """
    获取资金流动分析数据
    
    Args:
        request: 包含开始日期和结束日期的请求对象
        
    Returns:
        CapitalFlowResponse: 包含所有资金流动相关数据的响应对象
    """
    try:
        # 这里应该根据日期范围查询数据库
        # 目前返回JSON格式的数据结构
        data = await request.json()
        from src.utils.DB.webReturn import getCashFlowData
        return getCashFlowData(data)
    except Exception as e:
        logging.error(f"Error in get_capital_flow_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
        
@app.post("/api/budget-data")
async def get_budget_data(request: Request):
    try:
        data = await request.json()
        from src.utils.DB.mainDB import mainDB
        try:
            if data["dataSource"]=="balance":
                rows = mainDB().getCloseDataByBalance()
                return rows
            elif data["dataSource"]=="detail":
                year=data["year"]
                month=data["month"]
                rows = mainDB().getCloseComputeByDetailLedger(year,month)
                return rows
        except Exception as e:
            #打印所有出错位置
            import traceback
            traceback.print_exc()
            return [["错误"],[
                "错误"]]
    except Exception as e:
        print("发生错误", e)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/budget-data-difference")
async def get_budget_data_difference():
    try:
        from src.utils.DB.midIntrSQLiteDB import excelDB
        conn=excelDB().conn
        rows=conn.execute("select * from 累计过账收入成本").fetchall()
        conn.close()
        resultMap={}
        for i in range(len(rows)):
            resultMap[f"{rows[i][0]}{rows[i][1]}"]={"收入":rows[i][2],"成本":rows[i][3]}
        return resultMap
    except Exception as e:
        print("发生错误", e)
        raise HTTPException(status_code=500, detail=str(e))
        

@app.post("/api/save-snapsheet")
async def save_snapsheet(request: Request):
    try:
        data = await request.json()
        result = dataprocessing.saveDataProcess(data)
        return {"status": "success", "result": result}
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/delete-snapsheet")
async def save_snapsheet(request: Request):
    try:
        data = await request.json()
        result = dataprocessing.deleteDataProcess(data)
        return {"status": "success", "result": result}
    except Exception as e:
        logging.error(f"Error in delete_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/save-snapsheet-new")
async def save_snapsheet_new(request: Request):
    try:
        data = await request.json()
        result = dataprocessing.saveDataProcess2(data)
        return {"status": "success", "result": result}
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/get-snapsheet")
async def get_snapsheet(request: Request):
    try:
        data = await request.json()
        result = dataprocessing.getDataProcess(data)
        return result
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/get-budget-data-close")
async def get_budget_data_close(request: Request):
    try:
        result = dataprocessing.getCloseDataProcess()
        return result
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/update-report-snapshot")
async def update_report_snapshot(request: Request):
    try:
        url="https://cscec3b-fip.hb11oss.ctyunxs.cn/web.txt" #更新web
        r = requests.get(url)
        if r.status_code==200:
            #以utf-8编码读取内容
            r = r.content.decode('utf-8')
            from src.utils.DB.midIntrSQLiteDB import webDB
            db=webDB()
            db.writeData("表格缓存", [r,"最新模板"])
            db.close()
        return {"status":True,"message":"更新成功"}
    except Exception as e:
        logging.error(f"Error in update_report_snapshot: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/get-available-periods")
async def get_snapsheet_periods(request: Request):
    try:
        result = dataprocessing.queryDatalist()
        return result
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/save-budget")
async def save_budget(request: Request):
    try:
        data = await request.json()
        result = dataprocessing.pushDataToColse(data)
        return result
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/save-config")
async def save_config(request: Request):
    try:
        data = await request.json()
        from src.utils.DB.configDB import configDB
        configDB().saveJson(data)
        return {"status": "success"}

    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/get-all-configs")
async def get_all_config(request: Request):
    try:
        from src.utils.DB.configDB import configDB
        return configDB().getJson()
    except Exception as e:
        logging.error(f"Error in get-all-configs: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/project/search")
async def get_project_list(request: Request):
    try:
        from src.utils.DB.mainDB import mainDB
        db=mainDB()
        value=db.conn.execute("select 利润中心 as id,利润中心描述 as name,项目编码 as code from 主数据 ").fetchall()
        title=[row[0] for row in db.conn.description]
        db.conn.close()
        #json化
        prjectList=[]
        for i in range(len(value)):
            prjectList.append({"id":value[i][0],"name":value[i][1],"code":value[i][2]})
        return prjectList
    except Exception as e:
        logging.error(f"Error in get-project-list: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/project/fuzzy-match")
async def fuzzy_match_projects(request: Request):
    """项目名称模糊匹配API"""
    try:
        data = await request.json()
        query = data.get("query", "")
        top_k = data.get("top_k", 5)
        method = data.get("method", "hybrid")
        threshold = data.get("threshold", 0.1)
        use_ai = data.get("use_ai", False)

        if not query:
            raise HTTPException(status_code=400, detail="Query parameter is required")

        # 获取项目列表
        from src.utils.DB.mainDB import mainDB
        db = mainDB()
        projects_data = db.conn.execute("select 利润中心 as id,利润中心描述 as name,项目编码 as code from 主数据").fetchall()
        db.conn.close()

        # 构建项目列表
        project_list = []
        for row in projects_data:
            project_list.append({
                "id": row[0],
                "name": row[1],
                "code": row[2],
                "text": f"{row[1]} {row[2]}"  # 组合名称和编码用于匹配
            })

        if use_ai:
            # 使用AI增强的匹配
            from src.utils.ai.ollama import get_project_suggestions_with_ai
            result = get_project_suggestions_with_ai(query, project_list)
        else:
            # 仅使用模糊匹配
            from src.utils.ai.ollama import match_project_name
            matches = match_project_name(query, project_list, top_k, method, threshold)
            result = {
                "query": query,
                "fuzzy_matches": matches,
                "total_projects": len(project_list)
            }

        return result

    except Exception as e:
        logging.error(f"Error in fuzzy-match-projects: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/project/data")
async def get_project_data(request: Request):
    try:
        data = await request.json()
        projectId=data["projectId"]
        from src.utils.DB.mainDB import mainDB
        db=mainDB()
        return db.queryDataByProject(projectId)

    except Exception as e:
        logging.error(f"Error in get-project-data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

def getFlowID(flowID):
    import re
    text = flowID  # example text
    pattern = r'^([a-zA-Z]{1,5}\d{9,20})$'
    match = re.match(pattern, text)
    result=None
    if match:
        result = match.group(1)  # The captured value
    return result
@app.post("/api/flowID")
async def get_flowID(request: Request):
    try:
        data = await request.json()
        flowIDList=data["inputs"]
        id=""
        for flowID in flowIDList:
            result=getFlowID(flowID)
            if result:
                id=result
        from src.utils.DB.mainDB import mainDB
        db=mainDB()
        result=db.conn.execute("select 过帐日期 from 明细帐 where 中台单据号=?", (id,)).fetchall()[0][0]
        db.conn.close()
        return id + "过账日期为:" + str(result)

    except Exception as e:
        logging.error(f"api/flowID: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/reconciliation/execute")
async def get_offset(request: Request):
    try:
        data = await request.json()
        tabType=data["tabType"]
        from src.utils.closeAccountingPeriod.offset import reportOffset,bookOffsetSituation
        if tabType=="reportOffset":
            data=reportOffset()
        elif tabType=="internalOffset":
            data=bookOffsetSituation()
        return {
        "success": True,
        "message": "操作成功",
        "data": data
        }
    except Exception as e:
        logging.error(f"api/reconciliation/execute: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))


def run_server():
    uvicorn.run(app, host="127.0.0.1", port=8000,log_config=None) #注意没有cmd输出的情况下，必需将log_config设置为None,不然日志不输出服务器报错
    import webview
    window = webview.create_window("进程监控示例", url="http://127.0.0.1:8000")
    webview.start(window)
    
    #,log_config=None

def start_server():
    # 创建服务器进程
    #multiprocessing.set_executable(sys.executable)
    #sys._base_executable = os.path.join(sys.exec_prefix, 'pythonw.exe')
    #multiprocessing.set_executable(os.path.join(sys.exec_prefix, 'pythonw.exe'))
    server_process = multiprocessing.Process(target=run_server)
    server_process.daemon = True  # 设置为守护进程，主进程退出时自动关闭
    server_process.start()
    return server_process
