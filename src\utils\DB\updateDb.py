import duckdb 
import src.base.settings as settings
import src.utils.Excel.excel as excel
import src.utils.sapPublic.sapExport
import src.utils.DB.readtxttolist
import src.utils.DB.outputSQL  as Sql
import pandas as pd
import tkinter.filedialog
import os

def downloadFromDb(tableName):
    print("查询"+tableName+"表")
    con = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=True)
    if tableName=="异常数据":
        df=con.execute(f"select * from 明细帐 where 利润中心 not like 'L%'").df()
    else:
        df=con.execute(f"select * from {tableName}").df()


    #将df转为一个二维list
    try:
        wb=excel.myBook()
        ws=wb.sheet("数据库取数")
        dfList=[df.columns.to_list()]+df.values.tolist()
        ws.Range("a1").resize(len(dfList),len(dfList[0])).Value=dfList
    except:
        print("未发现活动打开数据库取数工作表")
        df.to_excel(settings.PATH_QUERY+f"/{tableName}.xlsx",index=False,sheet_name=tableName)

def updateToDb(tableName:str,fileChoose=False):
    print("更新"+tableName+"表")
    if fileChoose:
        import src.utils.fileui as fileui
        filePath=fileui.select_file()
        df=pd.read_excel(filePath)
    else:
        print("未发现活动打开"+tableName+"工作表")
        print("从文件中读取数据")
        df=pd.read_excel(settings.PATH_QUERY+f"/{tableName}.xlsx",sheet_name=tableName)
    con = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)
    if tableName=="异常数据":
        con.execute(f"INSERT OR REPLACE INTO 明细帐 SELECT * from df ")
    else:
        con.execute(f"INSERT OR REPLACE INTO {tableName} SELECT * from df ")


def updateMDtable():
    pathList=src.utils.sapPublic.sapExport.exportMain("导出主数据")
    for path in pathList:
        dfList=src.utils.DB.readtxttolist.readTxtForMDdata(path)
        df=pd.DataFrame(dfList[1:],columns=dfList[0])
        con = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)
        con.execute(Sql.主数据写入)

def updateInternalTable(iffileDialog=False):
    printList=[]
    if iffileDialog:
        filePath=tkinter.filedialog.askopenfilenames(initialdir=settings.PATH_DOWNLOAD+"/导出内部对账数据")
    else:
        filePath=src.utils.sapPublic.sapExport.exportMain("导出内部对账1")
    for path in filePath:
        try:
            arr=src.utils.DB.readtxttolist.readTxtInerTransaction(path)
            if arr!=None:
                if len(printList)==0 :
                    printList=arr
                else:
                    printList=printList+arr[1:]
        except Exception as e:
            print("DB.updateDb第49行错误")
            print(e)
    df=pd.DataFrame(printList[1:],columns=printList[0])
    con=duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)
    df1=con.execute(Sql.内部对账).df()
    con.execute("INSERT OR REPLACE INTO 内部对账 SELECT * from df1")

def updateFipContarctTable():
    print("注意等待结束，会自动更新数据库")
    import src.utils.fundCapital.自动导出财务一体化合同台账
    df1=src.utils.fundCapital.自动导出财务一体化合同台账.auto(False)
    con=duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)
    con.execute("INSERT OR REPLACE INTO 一体化合同台账 SELECT * from df1")
    con.close()


def updateAccountMapTable():
    #更新科目对照
    import requests
    r = requests.get("https://cscec3b-fip.hb11oss.ctyunxs.cn/科目对照.xlsx")
    if r.status_code==200:
        with open(settings.PATH_EXCEL+"/科目对照.xlsx", 'wb') as f:
            f.write(r.content)
        df=pd.read_excel(settings.PATH_EXCEL+"/科目对照.xlsx",sheet_name="科目对照")
        os.remove(settings.PATH_EXCEL+"/科目对照.xlsx")
        con=duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)
        con.execute("INSERT OR REPLACE INTO 科目对照 SELECT * from df")
        con.close()
        print("科目对照更新成功")
    else:
        print("科目对照更新失败")