
import src.base.settings as settings
import src.utils.DB.midIntrSQLiteDB as midIntrSQLiteDB
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd
from src.utils.DB.mainDB import mainDB

def closeDataProcess(data:list):
    pass

def saveDataProcess(data):
    conn=midIntrSQLiteDB.webDB()
    conn.writeData("表格缓存",data)
    conn.close()

def deleteDataProcess(data):
    conn=midIntrSQLiteDB.webDB()
    conn.deleteData("表格缓存",data)
    conn.close()
def saveDataProcess2(data):
    conn=midIntrSQLiteDB.webDB()
    conn.updateLastData("表格缓存",data)
    conn.close()

def getDataProcess(request):
    conn=midIntrSQLiteDB.webDB()
    primaryID=request["periodId"]
    data=conn.queryData("表格缓存",primaryID)
    conn.close()
    return data

def queryDatalist():
    conn=midIntrSQLiteDB.webDB()
    data=conn.queryDatalist("表格缓存")
    conn.close()
    return data


def pushDataToColse(datas:dict):
    conn=excelDB()
    for key,value in datas.items():
        #修正空白列
        for i in range(len(value[0])):
            if value[0][i] is None or value[0][i]=="" or value[0][i]==" ":
                value[0][i]="unnamed_"+str(i)
        df=pd.DataFrame(value[1:],columns=value[0])
        df.to_sql(key,conn.conn,if_exists="replace",index=False)
    conn.close()
        
def getCloseDataProcess():
    conn=excelDB()
    df1=pd.read_sql("select * from 独立结账模板安全费",conn.conn)
    df2=pd.read_sql("select * from 独立结账模板收入成本",conn.conn)
    df3=pd.read_sql("select * from 批量暂估",conn.conn)
    df1.fillna("",inplace=True)
    df2.fillna("",inplace=True)
    df3.fillna("",inplace=True)
    conn.close()
    return [[df1.columns.tolist()]+df1.values.tolist(),[df2.columns.tolist()]+df2.values.tolist(),[df3.columns.tolist()]+df3.values.tolist()]

def getCloseDataProcess2():
    conn=excelDB()
    columns1=["序号","项目状态","组织机构","利润中心编码","项目名称","项目编码","內部外部","SAP收入测算是否","SAP确认是否","一体化收入确认是否","SAP最终过账是否","匡算单据号","年份","月份","事由","消息列","结账预计收入","结账预计成本","收入合同编号","税码","按比例结账成本","按比例结账收入","审批人1","审批人2","审批人3"]
    data1=["1","在建","中建三局智能技术有限公司","L307900364","新疆温宿制绿氢总承包项目-智能公司","1220178251","内部","是","是","是","是","不填",2025,	7,"新疆温宿制绿氢总承包项目-智能公司2025年5月收入确认","不填",288276881.47,288473996.55,"中建三局020020240163020001","xc",0,0,"10028717","10028717","10028717"]
    columns2=["序号","组织机构","项目名称","事由","专项储备计提比例","专项储备余额","专项储备基数","安全费计提审批人1","安全费计提审批人2","安全费计提审批人3","是否"]
    data2=["1","中建三局安装工程有限公司南方大区","南方珠海科瑞思全自动精密磁性元器件绕线设备升级项目","南方珠海科瑞思全自动精密磁性元器件绕线设备升级项目计提2025年4月安全生产经费",3,"10272.8","=ROUND(F2/E2*100,2)","","","注意，不需要选审批人则不填","是"]
    columns3=["序号","组织机构","项目名称","事由","金额","合同编号","合同类型","暂估成本科目","付款比例","是否"]
    data3=["1","中建三局安装工程有限公司南方大区","南方联通粤港澳大湾区枢纽韶关数据中心项目一期工程","南方联通粤港澳大湾区枢纽韶关数据中心项目一期工程红冲分包工程款2024.11","0","中建三局020020230399040001","分包","如果前面是其他经营则要填","100","是"]			
    
    try:
        df1=pd.read_sql("select * from 独立结账模板安全费",conn.conn)
        df2=pd.read_sql("select * from 独立结账模板收入成本",conn.conn)
        df3=pd.read_sql("select * from 批量暂估",conn.conn)
    except:
        df1=pd.DataFrame(columns=columns1,data=[data1])
        df2=pd.DataFrame(columns=columns2,data=[data2])
        df3=pd.DataFrame(columns=columns3,data=[data3])
    df1.fillna("",inplace=True)
    df2.fillna("",inplace=True)
    df3.fillna("",inplace=True)
    conn.close()
    return {"独立结账模板安全费":[df1.columns.tolist()]+df1.values.tolist(),"独立结账模板收入成本":[df2.columns.tolist()]+df2.values.tolist(),"批量暂估":[df3.columns.tolist()]+df3.values.tolist()}

def writeDataProcess(data):
    conn=excelDB()
    data1=data["独立结账模板安全费"]
    data2=data["独立结账模板收入成本"]
    data3=data["批量暂估"]
    processed_columns1 = [f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(data1[0])]
    processed_columns2 = [f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(data2[0])]
    processed_columns3 = [f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(data3[0])]
    df1 = pd.DataFrame(columns=processed_columns1,data=data1[1:])
    df2 = pd.DataFrame(columns=processed_columns2,data=data2[1:])
    df3 = pd.DataFrame(columns=processed_columns3,data=data3[1:])
    df1.to_sql("独立结账模板安全费",conn.conn,if_exists='replace',index=False)
    df2.to_sql("独立结账模板收入成本",conn.conn,if_exists='replace',index=False)
    df3.to_sql("批量暂估",conn.conn,if_exists='replace',index=False)
    conn.close()

def safetyFee():
    conn=excelDB()
    dfOrgin=mainDB().getCloseDataByBalance()[0]
    df=pd.DataFrame(columns=dfOrgin[0],data=dfOrgin[1:])
    df2=df[["利润中心组名称","项目名称","专项储备余额"]]
    df2 = df2[abs(df2["专项储备余额"]) > 0.001]
    df2.rename({"利润中心组名称":"组织机构"})
    df2.fillna("",inplace=True)
    conn.close()
    return [df2.columns.tolist()]+df2.values.tolist()
