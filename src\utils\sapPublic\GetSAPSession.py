
import time
import win32com.client
from src.utils.DB.configDB import configDB
import subprocess

def openSap(sapPath=None,AccountSetName=None,user=None,pwd=None):
    config_map=configDB().sapConnection
    sapPath=config_map["path"].replace("saplogon.exe","sapshcut.exe").replace('"', '')
    AccountSetName=config_map["accountBook"]
    user=config_map["username"]
    pwd=config_map["password"]
    command=f'''"{sapPath}" -system=00 -sysname={AccountSetName}  -client=800 -user={user} -password="{pwd}"  -language=ZH'''
    returnText=subprocess.run(command, shell=True, capture_output=True, text=True)
    #print(returnText)
    print("登录SAP")


def creatSAP():
        try:
            SapGuiAuto = win32com.client.GetObject("SAPGUI")
            application = SapGuiAuto.GetScriptingEngine
            connection = application.Children(0)
            #session=connection.Children(0)
        except Exception as e:
            print("先关闭再打开")
            try:
                subprocess.run(["taskkill", "/F", "/IM", "saplogon.exe", "/T"], check=True)
            except:
                pass
            openSap()
 
        td=600
        while td>0:
            try:
                SapGuiAuto = win32com.client.GetObject("SAPGUI")
                application = SapGuiAuto.GetScriptingEngine
                connection = application.Children(0)
                session=connection.Children(0)
                td=-1
                print("成功获取到session")
            except:
                td=td-1
                time.sleep(0.1)
        #获取会话
        try:
            #获取连接下的所有 session
            allSessions = connection.Children
            sessionCount = allSessions.Count
            print(sessionCount)

            #遍历所有 session
            for i in range(sessionCount):
                session = allSessions(i)
                # 这里可以对每个 session 进行操作
                # 例如：session.findById("wnd[0]").maximize
                session.findById("wnd[1]/usr/radMULTI_LOGON_OPT2").Select()
                session.findById("wnd[1]/usr/lbl%#AUTOTEXT008").SetFocus()
                session.findById("wnd[1]/usr/lbl%#AUTOTEXT008").caretPosition = 43
                session.findById("wnd[1]/tbar[0]/btn[0]").press()
                print("打开完成")
        except Exception as e:
            print("尝试打开")
        return session

def connect_SAP(maxSessionNum):
    '''连接SAP，并创建maxSessionNum个session会话，返回session列表（含session索引）'''
    SapGuiAuto = win32com.client.GetObject("SAPGUI")
    application = SapGuiAuto.GetScriptingEngine
    connection = application.Children(0)
    indexSessions=[]
    session=connection.Children(0)
    try:
        session.findById("wnd[1]/usr/radMULTI_LOGON_OPT2").Select()
        session.findById("wnd[1]/usr/lbl%#AUTOTEXT008").SetFocus()
        session.findById("wnd[1]/usr/lbl%#AUTOTEXT008").caretPosition = 43
        session.findById("wnd[1]/tbar[0]/btn[0]").press()
        print("已关闭重复登陆提示")
    except Exception as e:
        pass
    cnt=connection.Children.Count
    if cnt<maxSessionNum:
        for _ in range(maxSessionNum-cnt):
            session.Createsession()
            time.sleep(0.5)

