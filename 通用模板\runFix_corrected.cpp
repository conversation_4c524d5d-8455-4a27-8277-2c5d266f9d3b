#include <windows.h>
#include <iostream>
#include <string>
#include <fstream>
#include <sstream>
#include <cstdio>
#include <memory>
#include <stdexcept>
#include <algorithm>

class PythonRunner {
private:
    std::string runtimePath;
    std::string tempScriptPath;

public:
    PythonRunner() {
        // 获取当前程序所在目录
        char buffer[MAX_PATH];
        GetModuleFileNameA(NULL, buffer, MAX_PATH);
        std::string exePath(buffer);
        size_t pos = exePath.find_last_of("\\/");
        std::string currentDir = exePath.substr(0, pos);

        // 设置runtime目录下的python.exe路径
        runtimePath = currentDir + "\\runtime\\python.exe";
        tempScriptPath = currentDir + "\\temp_script.py";
    }

    // 显示错误弹窗
    void showErrorDialog(const std::string& title, const std::string& message) {
        MessageBoxA(NULL, message.c_str(), title.c_str(), MB_ICONERROR | MB_OK);
    }

    // 显示信息弹窗
    void showInfoDialog(const std::string& title, const std::string& message) {
        MessageBoxA(NULL, message.c_str(), title.c_str(), MB_ICONINFORMATION | MB_OK);
    }

    // 检查python.exe是否存在
    bool checkPythonExists() {
        DWORD fileAttr = GetFileAttributesA(runtimePath.c_str());
        return (fileAttr != INVALID_FILE_ATTRIBUTES && !(fileAttr & FILE_ATTRIBUTE_DIRECTORY));
    }

    // 将Python代码写入临时文件
    bool writeTempScript(const std::string& pythonCode) {
        std::ofstream file(tempScriptPath);
        if (!file.is_open()) {
            showErrorDialog("文件错误", "无法创建临时Python脚本文件");
            return false;
        }
        file << pythonCode;
        file.close();
        return true;
    }

    // 执行命令并获取输出
    std::string executeCommand(const std::string& command) {
        std::string result;

        SECURITY_ATTRIBUTES sa;
        sa.nLength = sizeof(SECURITY_ATTRIBUTES);
        sa.lpSecurityDescriptor = NULL;
        sa.bInheritHandle = TRUE;

        HANDLE hRead, hWrite;
        if (!CreatePipe(&hRead, &hWrite, &sa, 0)) {
            showErrorDialog("管道错误", "无法创建管道");
            return "";
        }

        STARTUPINFOA si;
        PROCESS_INFORMATION pi;
        ZeroMemory(&si, sizeof(si));
        ZeroMemory(&pi, sizeof(pi));
        si.cb = sizeof(si);
        si.hStdError = hWrite;
        si.hStdOutput = hWrite;
        si.dwFlags = STARTF_USESTDHANDLES;

        // 创建进程
        if (!CreateProcessA(NULL, const_cast<char*>(command.c_str()), NULL, NULL, TRUE, 0, NULL, NULL, &si, &pi)) {
            showErrorDialog("进程错误", "无法启动Python进程");
            CloseHandle(hRead);
            CloseHandle(hWrite);
            return "";
        }

        CloseHandle(hWrite);

        // 读取输出
        char buffer[4096];
        DWORD bytesRead;
        while (ReadFile(hRead, buffer, sizeof(buffer) - 1, &bytesRead, NULL) && bytesRead > 0) {
            buffer[bytesRead] = '\0';
            result += buffer;
        }

        // 等待进程结束
        WaitForSingleObject(pi.hProcess, INFINITE);

        DWORD exitCode;
        GetExitCodeProcess(pi.hProcess, &exitCode);

        CloseHandle(hRead);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);

        // 如果进程非正常退出，显示错误信息
        if (exitCode != 0) {
            std::string errorMsg = "Python脚本执行失败\n退出代码: " + std::to_string(exitCode);
            if (!result.empty()) {
                errorMsg += "\n错误信息:\n" + result;
            }
            showErrorDialog("Python执行错误", errorMsg);
        }

        return result;
    }

    // 运行Python代码
    bool runPythonCode(const std::string& pythonCode) {
        // 检查python.exe是否存在
        if (!checkPythonExists()) {
            showErrorDialog("Python错误", "找不到Python解释器\n路径: " + runtimePath);
            return false;
        }

        // 写入临时脚本文件
        if (!writeTempScript(pythonCode)) {
            return false;
        }

        // 构建命令
        std::string command = "\"" + runtimePath + "\" \"" + tempScriptPath + "\"";

        // 执行Python脚本
        std::string output = executeCommand(command);

        // 删除临时文件
        DeleteFileA(tempScriptPath.c_str());

        // 如果有输出且不是错误，显示输出
        if (!output.empty()) {
            // 简单判断是否为正常输出（不包含常见错误关键词）
            std::string lowerOutput = output;
            std::transform(lowerOutput.begin(), lowerOutput.end(), lowerOutput.begin(), ::tolower);

            if (lowerOutput.find("traceback") == std::string::npos &&
                lowerOutput.find("error") == std::string::npos &&
                lowerOutput.find("exception") == std::string::npos) {
                showInfoDialog("Python输出", output);
            }
        }

        return true;
    }

    // 清理临时文件
    void cleanup() {
        DeleteFileA(tempScriptPath.c_str());
    }
};

// 示例使用函数
void runExamplePython() {
    PythonRunner runner;

    // 示例Python代码
    std::string pythonCode = R"(
import sys
print("Hello from Python!")
print("Python版本:", sys.version)
print("当前工作目录:", __file__ if '__file__' in globals() else "临时脚本")

# 示例计算
result = 10 + 20
print(f"计算结果: 10 + 20 = {result}")

# 可以在这里添加更复杂的逻辑
try:
    import requests
    print("requests库可用")
except ImportError:
    print("requests库不可用")
)";

    runner.runPythonCode(pythonCode);
}

// 从文件读取Python代码并运行
void runPythonFromFile(const std::string& filePath) {
    PythonRunner runner;

    std::ifstream file(filePath);
    if (!file.is_open()) {
        runner.showErrorDialog("文件错误", "无法打开Python脚本文件: " + filePath);
        return;
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string pythonCode = buffer.str();
    file.close();

    runner.runPythonCode(pythonCode);
}

// 主函数
int main() {
    // 设置控制台代码页为UTF-8以支持中文显示
    SetConsoleOutputCP(CP_UTF8);

    std::cout << "Python Code Executor" << std::endl;
    std::cout << "==================" << std::endl;

    int choice;
    std::cout << "Please select operation:" << std::endl;
    std::cout << "1. Run example Python code" << std::endl;
    std::cout << "2. Run Python code from file" << std::endl;
    std::cout << "3. Manually input Python code" << std::endl;
    std::cout << "Please enter choice (1-3): ";

    std::cin >> choice;
    std::cin.ignore(); // 忽略换行符

    PythonRunner runner;

    switch (choice) {
        case 1:
            runExamplePython();
            break;

        case 2: {
            std::string filePath;
            std::cout << "Please enter Python file path: ";
            std::getline(std::cin, filePath);
            runPythonFromFile(filePath);
            break;
        }

        case 3: {
            std::string pythonCode;
            std::cout << "Please enter Python code (type 'END' to finish):" << std::endl;
            std::string line;
            while (std::getline(std::cin, line) && line != "END") {
                pythonCode += line + "\n";
            }
            runner.runPythonCode(pythonCode);
            break;
        }

        default:
            runner.showErrorDialog("Input Error", "Invalid choice");
            break;
    }

    std::cout << "Press any key to exit...";
    std::cin.get();
    return 0;
}