import os
import requests
import sys
import threading
import tkinter as tk
from tkinter import ttk, messagebox

def download_files(progress_var, status_var, button):
    try:
        path = os.path.dirname(sys.executable)
        files = [
            ('https://cscec3b-fip.hb11oss.ctyunxs.cn/ftools.zip', os.path.join(path, 'ftools2.zip')),
            ('https://cscec3b-fip.hb11oss.ctyunxs.cn/main2.pyc', os.path.join(path, 'main2.pyc'))
        ]
        total = len(files)
        for i, (url, save_path) in enumerate(files, 1):
            status_var.set('Downloading: ' + os.path.basename(save_path))
            response = requests.get(url)
            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    f.write(response.content)
            else:
                status_var.set('Download failed: ' + os.path.basename(save_path))
                messagebox.showerror('Error', os.path.basename(save_path) + ' download failed!')
                button.config(state=tk.NORMAL)
                return
            progress_var.set(int(i/total*100))
        status_var.set('Complete!')
        messagebox.showinfo('Info', 'All files downloaded!')
    except Exception as e:
        status_var.set('Error!')
        messagebox.showerror('Error', str(e))
    finally:
        button.config(state=tk.NORMAL)

def start_download(progress_var, status_var, button):
    button.config(state=tk.DISABLED)
    threading.Thread(target=download_files, args=(progress_var, status_var, button), daemon=True).start()

def main():
    root = tk.Tk()
    root.title('File Downloader')
    root.geometry('350x150')
    progress_var = tk.IntVar()
    status_var = tk.StringVar(value='Ready...')
    ttk.Label(root, textvariable=status_var).pack(pady=10)
    progressbar = ttk.Progressbar(root, length=250, variable=progress_var, maximum=100)
    progressbar.pack(pady=10)
    download_btn = ttk.Button(root, text='Start Download', 
                            command=lambda: start_download(progress_var, status_var, download_btn))
    download_btn.pack(pady=10)
    root.mainloop()

if __name__ == '__main__':
    main()
